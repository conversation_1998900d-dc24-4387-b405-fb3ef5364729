#!/bin/bash

# 股票TOP20监控Web应用启动脚本

echo "🚀 启动股票TOP20监控Web应用..."

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先创建虚拟环境"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import fastapi, uvicorn, passlib" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要依赖，请运行以下命令安装："
    echo "pip install fastapi uvicorn passlib[bcrypt] python-jose python-multipart"
    exit 1
fi

# 启动Web应用
echo "🌐 启动Web服务器..."
echo "📍 访问地址: http://127.0.0.1:8001"
echo "👤 测试账号:"
echo "   管理员 - 用户名: admin, 密码: admin123"
echo "   普通用户 - 用户名: user, 密码: user123"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=" * 50

python3 -m uvicorn web_app:app --host 127.0.0.1 --port 8001
