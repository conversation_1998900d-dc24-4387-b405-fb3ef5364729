#!/usr/bin/env python3
"""
数据库初始化脚本
确保所有必要的表都存在
"""

import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库表"""
    try:
        engine = create_engine(Config.DATABASE_URL)
        
        with engine.connect() as conn:
            logger.info("开始初始化数据库...")
            
            # 1. 创建trading_dates表
            logger.info("创建trading_dates表...")
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS trading_dates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    trade_date DATE NOT NULL UNIQUE,
                    is_trading_day TINYINT(1) NOT NULL DEFAULT 1,
                    year INT NOT NULL,
                    month INT NOT NULL,
                    day INT NOT NULL,
                    weekday INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_trade_date (trade_date),
                    INDEX idx_year_month (year, month),
                    INDEX idx_is_trading_day (is_trading_day)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='交易日历表'
            """))
            
            # 2. 创建stock_daily_data表（如果不存在）
            logger.info("创建stock_daily_data表...")
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS stock_daily_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(20) NOT NULL,
                    stock_name VARCHAR(100),
                    trade_day DATE NOT NULL,
                    sector TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_stock_code (stock_code),
                    INDEX idx_trade_day (trade_day),
                    UNIQUE KEY uk_stock_trade_day (stock_code, trade_day)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基础数据表'
            """))
            
            # 3. 创建stock_kline_data表（如果不存在）
            logger.info("创建stock_kline_data表...")
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS stock_kline_data (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(20) NOT NULL,
                    trade_date DATE NOT NULL,
                    open_price DECIMAL(10,3),
                    high_price DECIMAL(10,3),
                    low_price DECIMAL(10,3),
                    close_price DECIMAL(10,3),
                    volume BIGINT,
                    amount DECIMAL(20,2),
                    turnover_rate DECIMAL(8,4),
                    ma5 DECIMAL(10,3),
                    ma10 DECIMAL(10,3),
                    ma20 DECIMAL(10,3),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_stock_code (stock_code),
                    INDEX idx_trade_date (trade_date),
                    UNIQUE KEY uk_stock_trade_date (stock_code, trade_date)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票K线数据表'
            """))
            
            # 4. 创建new_strategy_results表（如果不存在）
            logger.info("创建new_strategy_results表...")
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS new_strategy_results (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    stock_code VARCHAR(20) NOT NULL,
                    stock_name VARCHAR(50),
                    concepts VARCHAR(500),
                    entry_date DATE NOT NULL,
                    entry_price DECIMAL(10,3) NOT NULL,
                    previous_high_date DATE NOT NULL,
                    previous_high_price DECIMAL(10,3) NOT NULL,
                    lowest_point_date DATE NOT NULL,
                    lowest_point_price DECIMAL(10,3) NOT NULL,
                    pullback_ratio DECIMAL(8,2) NOT NULL,
                    distance_to_high DECIMAL(8,2) NOT NULL,
                    distance_to_high_ratio DECIMAL(8,2) NOT NULL,
                    time_gap_days INT NOT NULL,
                    current_ma5 DECIMAL(10,3),
                    current_ma10 DECIMAL(10,3),
                    volume BIGINT,
                    turnover_rate DECIMAL(8,4),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_stock_code (stock_code),
                    INDEX idx_entry_date (entry_date),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新策略筛选结果表'
            """))
            
            # 5. 创建sync_progress表（如果不存在）
            logger.info("创建sync_progress表...")
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS sync_progress (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    sync_date DATE NOT NULL,
                    total_stocks INT NOT NULL,
                    completed_stocks INT DEFAULT 0,
                    failed_stocks INT DEFAULT 0,
                    completed_codes TEXT,
                    failed_codes TEXT,
                    status VARCHAR(20) DEFAULT 'running',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_sync_date (sync_date),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步进度表'
            """))
            
            # 6. 初始化基础交易日数据（如果表为空）
            result = conn.execute(text("SELECT COUNT(*) FROM trading_dates"))
            count = result.fetchone()[0]
            
            if count == 0:
                logger.info("初始化基础交易日数据...")
                init_trading_dates(conn)
            else:
                logger.info(f"trading_dates表已有 {count} 条数据，跳过初始化")
            
            conn.commit()
            logger.info("✅ 数据库初始化完成")
            
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {str(e)}")
        raise

def init_trading_dates(conn):
    """初始化交易日数据"""
    try:
        # 生成2025年的基础交易日数据（排除周末）
        start_date = datetime(2025, 1, 1)
        end_date = datetime(2025, 12, 31)
        
        current_date = start_date
        trading_dates = []
        
        while current_date <= end_date:
            # 排除周末
            is_trading = current_date.weekday() < 5  # 0-4是周一到周五
            
            trading_dates.append({
                'trade_date': current_date.date(),
                'is_trading_day': 1 if is_trading else 0,
                'year': current_date.year,
                'month': current_date.month,
                'day': current_date.day,
                'weekday': current_date.weekday()
            })
            
            current_date += timedelta(days=1)
        
        # 批量插入
        for date_info in trading_dates:
            conn.execute(text("""
                INSERT INTO trading_dates 
                (trade_date, is_trading_day, year, month, day, weekday)
                VALUES 
                (:trade_date, :is_trading_day, :year, :month, :day, :weekday)
            """), date_info)
        
        logger.info(f"初始化了 {len(trading_dates)} 条交易日数据")
        
        # 手动标记一些已知的节假日为非交易日
        holidays = [
            '2025-01-01',  # 元旦
            '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31',  # 春节
            '2025-02-03', '2025-02-04', '2025-02-05',
            '2025-04-05', '2025-04-06', '2025-04-07',  # 清明节
            '2025-05-01', '2025-05-02', '2025-05-05',  # 劳动节
            '2025-06-09', '2025-06-10',  # 端午节
            '2025-09-15', '2025-09-16', '2025-09-17',  # 中秋节
            '2025-10-01', '2025-10-02', '2025-10-03',  # 国庆节
            '2025-10-06', '2025-10-07', '2025-10-08'
        ]
        
        for holiday in holidays:
            conn.execute(text("""
                UPDATE trading_dates 
                SET is_trading_day = 0 
                WHERE trade_date = :holiday
            """), {'holiday': holiday})
        
        logger.info(f"标记了 {len(holidays)} 个节假日为非交易日")
        
    except Exception as e:
        logger.error(f"初始化交易日数据失败: {str(e)}")
        raise

def main():
    """主函数"""
    print("🚀 数据库初始化脚本")
    print("=" * 50)
    
    try:
        init_database()
        print("✅ 数据库初始化成功")
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
