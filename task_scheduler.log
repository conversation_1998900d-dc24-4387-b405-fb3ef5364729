2025-08-05 23:44:34,172 - __main__ - INFO - 🚀 任务调度器启动
2025-08-05 23:44:34,173 - __main__ - ERROR - 发送钉钉消息异常: No connection adapters were found for '1https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55'
2025-08-05 23:44:34,173 - __main__ - INFO - 📅 任务已调度：
2025-08-05 23:44:34,173 - __main__ - INFO -   - 每日任务：每日凌晨7:30执行
2025-08-05 23:44:34,173 - __main__ - INFO -   - TOP20监控：交易时间内每分钟执行
2025-08-05 23:44:34,173 - __main__ - INFO - ⏰ 等待调度时间...
2025-08-05 23:45:35,653 - __main__ - INFO - 🚀 任务调度器启动
2025-08-05 23:45:35,938 - __main__ - INFO - 钉钉消息发送成功: 任务调度器启动
2025-08-05 23:45:35,939 - __main__ - INFO - 📅 任务已调度：
2025-08-05 23:45:35,939 - __main__ - INFO -   - 每日任务：每日凌晨7:30执行
2025-08-05 23:45:35,939 - __main__ - INFO -   - TOP20监控：交易时间内每分钟执行
2025-08-05 23:45:35,939 - __main__ - INFO - ⏰ 等待调度时间...
2025-08-05 23:45:53,034 - __main__ - INFO - ⚠️ 用户中断，任务调度器停止
2025-08-05 23:45:53,342 - __main__ - ERROR - 钉钉消息发送失败: {'errcode': 310000, 'errmsg': '错误描述:关键词不匹配;解决方案:请联系群管理员查看此机器人的关键词，并在发送的信息中包含此关键词;'}
2025-08-05 23:46:46,647 - __main__ - INFO - 🚀 任务调度器启动
2025-08-05 23:46:46,974 - __main__ - INFO - 钉钉消息发送成功: 任务调度器启动
2025-08-05 23:46:46,974 - __main__ - INFO - 📅 任务已调度：
2025-08-05 23:46:46,974 - __main__ - INFO -   - 每日任务：每日凌晨7:30执行
2025-08-05 23:46:46,974 - __main__ - INFO -   - TOP20监控：交易时间内每分钟执行
2025-08-05 23:46:46,974 - __main__ - INFO - ⏰ 等待调度时间...
2025-08-05 23:46:55,543 - __main__ - INFO - ⚠️ 用户中断，任务调度器停止
2025-08-05 23:46:55,853 - __main__ - ERROR - 钉钉消息发送失败: {'errcode': 310000, 'errmsg': '错误描述:关键词不匹配;解决方案:请联系群管理员查看此机器人的关键词，并在发送的信息中包含此关键词;'}
2025-08-05 23:49:56,904 - __main__ - INFO - 🚀 任务调度器启动
2025-08-05 23:49:57,224 - __main__ - INFO - 钉钉消息发送成功: 任务调度器启动
2025-08-05 23:49:57,225 - __main__ - INFO - 📅 任务已调度：
2025-08-05 23:49:57,225 - __main__ - INFO -   - 每日任务：每日凌晨7:30执行
2025-08-05 23:49:57,225 - __main__ - INFO -   - TOP20监控：交易时间内每分钟执行
2025-08-05 23:49:57,225 - __main__ - INFO - ⏰ 等待调度时间...
2025-08-05 23:50:57,231 - __main__ - INFO - ================================================================================
2025-08-05 23:50:57,234 - __main__ - INFO - 开始执行每日任务流程
2025-08-05 23:50:57,234 - __main__ - INFO - ================================================================================
2025-08-05 23:50:57,262 - __main__ - INFO - 2025-08-05 是交易日（今日或未来工作日，默认为交易日）
2025-08-05 23:50:57,578 - __main__ - INFO - 钉钉消息发送成功: 每日任务开始
2025-08-05 23:50:57,579 - __main__ - INFO - [1/4] 执行任务: 获取交易日期
2025-08-05 23:50:57,579 - __main__ - INFO - 开始执行脚本: get_trading_dates.py
2025-08-05 23:51:01,377 - __main__ - INFO - 脚本 get_trading_dates.py 输出:
2025-08-05 23:51:01,377 - __main__ - INFO -   已删除旧表 stock_market_data
2025-08-05 23:51:01,377 - __main__ - INFO -   已创建新表 stock_market_data，包含字段备注
2025-08-05 23:51:01,377 - __main__ - INFO -   成功保存 150 条数据到数据库
2025-08-05 23:51:01,377 - __main__ - INFO -   最近5个月的A股交易日日期(102天):
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-10
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-11
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-12
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-13
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-14
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-17
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-18
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-19
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-20
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-21
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-24
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-25
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-26
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-27
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-28
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-03-31
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-01
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-02
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-03
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-07
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-08
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-09
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-10
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-11
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-14
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-15
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-16
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-17
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-18
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-21
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-22
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-23
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-24
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-25
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-28
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-29
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-04-30
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-06
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-07
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-08
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-09
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-12
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-13
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-14
2025-08-05 23:51:01,377 - __main__ - INFO -   2025-05-15
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-16
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-19
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-20
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-21
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-22
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-23
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-26
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-27
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-28
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-29
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-05-30
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-03
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-04
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-05
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-06
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-09
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-10
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-11
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-12
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-13
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-16
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-17
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-18
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-19
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-20
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-23
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-24
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-25
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-26
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-27
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-06-30
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-01
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-02
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-03
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-04
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-07
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-08
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-09
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-10
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-11
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-14
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-15
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-16
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-17
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-18
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-21
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-22
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-23
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-24
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-25
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-28
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-29
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-30
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-07-31
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-08-01
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-08-04
2025-08-05 23:51:01,378 - __main__ - INFO -   2025-08-05
2025-08-05 23:51:01,378 - __main__ - INFO - ✅ 任务 获取交易日期 执行成功 (耗时: 3.8秒)
2025-08-05 23:51:01,378 - __main__ - INFO - [2/4] 执行任务: 获取股票基础数据
2025-08-05 23:51:01,378 - __main__ - INFO - 开始执行脚本: get_stock_data_by_day.py
2025-08-05 23:51:02,136 - __main__ - WARNING - 脚本 get_stock_data_by_day.py 错误输出:
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,697 - __main__ - INFO - ============================================================
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,697 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,697 - __main__ - INFO - ============================================================
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,752 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,757 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,757 - __main__ - INFO - 开始自动同步数据...
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,831 - __main__ - INFO - 发现 1 个日期需要同步数据: ['2025-08-04']
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,831 - __main__ - INFO - [1/1] 正在同步 2025-08-04 的数据...
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:01,831 - __main__ - INFO - 开始获取 2025-08-04 的股票数据
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,010 - __main__ - INFO - 成功获取到 11 条股票数据
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,042 - __main__ - INFO - 成功保存 11 条数据到数据库，日期: 2025-08-04
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,042 - __main__ - INFO - ✅ 2025-08-04 数据同步成功
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,042 - __main__ - INFO - 自动同步完成，成功同步 1/1 天的数据
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,042 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 23:51:02,137 - __main__ - WARNING -   2025-08-05 23:51:02,042 - __main__ - INFO - 程序执行完成！
2025-08-05 23:51:02,137 - __main__ - INFO - ✅ 任务 获取股票基础数据 执行成功 (耗时: 0.8秒)
2025-08-05 23:51:02,137 - __main__ - INFO - [3/4] 执行任务: 同步K线数据
2025-08-05 23:51:02,137 - __main__ - INFO - 开始执行脚本: sync_kline_by_concepts.py
2025-08-05 23:53:31,309 - __main__ - INFO - ⚠️ 用户中断，任务调度器停止
2025-08-05 23:53:31,599 - __main__ - ERROR - 钉钉消息发送失败: {'errcode': 310000, 'errmsg': '错误描述:关键词不匹配;解决方案:请联系群管理员查看此机器人的关键词，并在发送的信息中包含此关键词;'}
