#!/bin/bash

# 股票数据处理任务调度器启动脚本

echo "🚀 启动股票数据处理任务调度器"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查必要文件
required_files=(
    "task_scheduler.py"
    "get_trading_dates.py"
    "get_stock_data_by_day.py"
    "sync_kline_by_concepts.py"
    "new_strategy_screener.py"
    "config.py"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 文件检查完成"

# 检查Python依赖
echo "📦 检查Python依赖..."
python3 -c "
import sys
required_modules = ['pandas', 'sqlalchemy', 'pymysql', 'requests', 'schedule']
missing_modules = []

for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        missing_modules.append(module)

if missing_modules:
    print(f'❌ 缺少依赖模块: {missing_modules}')
    print('请运行: pip install -r requirements.txt')
    sys.exit(1)
else:
    print('✅ 依赖检查完成')
"

if [ $? -ne 0 ]; then
    exit 1
fi

# 创建日志目录
mkdir -p logs

# 测试数据库连接
echo "🔗 测试数据库连接..."
python3 -c "
from config import Config
from sqlalchemy import create_engine
try:
    engine = create_engine(Config.DATABASE_URL)
    with engine.connect() as conn:
        conn.execute('SELECT 1')
    print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    exit 1
fi

# 测试钉钉通知
echo "📱 测试钉钉通知..."
python3 -c "
from task_scheduler import DingTalkNotifier
webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55'
notifier = DingTalkNotifier(webhook_url)
success = notifier.send_message('调度器启动测试', '任务调度器正在启动...')
if success:
    print('✅ 钉钉通知正常')
else:
    print('⚠️ 钉钉通知可能有问题，但不影响启动')
"

echo "🎯 所有检查完成，启动任务调度器..."
echo "📅 调度时间: 每日凌晨2:00"
echo "📱 通知方式: 钉钉机器人"
echo "📝 日志文件: task_scheduler.log"
echo "=================================="

# 启动调度器
if [ "$1" = "--daemon" ]; then
    echo "🔄 后台模式启动..."
    nohup python3 task_scheduler.py > logs/scheduler_output.log 2>&1 &
    echo $! > logs/scheduler.pid
    echo "✅ 调度器已在后台启动 (PID: $(cat logs/scheduler.pid))"
    echo "📝 查看日志: tail -f logs/scheduler_output.log"
    echo "🛑 停止服务: kill $(cat logs/scheduler.pid)"
else
    echo "🔄 前台模式启动..."
    python3 task_scheduler.py
fi
