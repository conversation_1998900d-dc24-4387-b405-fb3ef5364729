#!/usr/bin/env python3
"""
发送策略股票TOP20到钉钉
简化版本，专门用于钉钉通知
"""

import time
import requests
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Top20DingTalkSender:
    """TOP20钉钉发送器"""
    
    def __init__(self):
        self.engine = create_engine(Config.DATABASE_URL)
        self.webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55"
    
    def get_tencent_price(self, code):
        """获取腾讯实时行情数据"""
        retry_count = 0
        max_retries = 3
        while retry_count < max_retries:
            try:
                stock_list = []
                URL = f"https://web.sqt.gtimg.cn/utf8/q={code}&offset=1,2,3,4,5,6,7,39,31,34,35,33,46,38,32"
                st = requests.get(URL).content
                a = (
                    st.decode("utf-8")
                    .replace('="1', "")
                    .replace('";', "~")
                    .replace("\n", "")
                    .split("~")
                )
                for x in range(0, len(a) - 1, 15):
                    stock_list.append(
                        {
                            "stock_code": a[x][4:10],
                            "stock_name": a[x + 1],
                            "close": a[x + 3],
                            "change_pct": a[x + 11],
                        }
                    )
                return stock_list

            except Exception as e:
                retry_count += 1
                if retry_count == max_retries:
                    raise Exception(f"获取腾讯行情数据失败，已重试{max_retries}次: {str(e)}")
                logger.warning(f"获取数据失败，10秒后进行第{retry_count + 1}次重试...")
                time.sleep(10)
    
    def get_recent_strategy_stocks(self, days=15):
        """获取最近N个交易日的策略结果股票"""
        try:
            with self.engine.connect() as conn:
                start_date = datetime.now() - timedelta(days=days + 10)
                
                query = text("""
                    SELECT DISTINCT stock_code, stock_name, concepts, entry_date ,entry_price
                    FROM new_strategy_results 
                    WHERE entry_date >= :start_date
                    ORDER BY entry_date DESC
                """)
                
                result = conn.execute(query, {'start_date': start_date.date()})
                rows = result.fetchall()
                
                stocks = []
                for row in rows:
                    stocks.append({
                        'stock_code': row[0],
                        'stock_name': row[1],
                        'concepts': row[2],
                        'entry_date': row[3],
                        'entry_price': row[4]
                    })
                
                logger.info(f"获取到 {len(stocks)} 个最近的策略股票")
                return stocks
                
        except Exception as e:
            logger.error(f"获取策略股票失败: {str(e)}")
            return []
    
    def format_stock_codes(self, stocks):
        """格式化股票代码，添加市场前缀"""
        formatted_codes = []
        
        for stock in stocks:
            code = stock['stock_code']
            
            if code.startswith('00') or code.startswith('30'):
                formatted_code = f"sz{code}"
            elif code.startswith('60') or code.startswith('68'):
                formatted_code = f"sh{code}"
            else:
                formatted_code = f"sz{code}"
            
            formatted_codes.append(formatted_code)
        
        return formatted_codes


    def get_realtime_data_and_send_top20(self, days=15):
        """获取实时数据并发送TOP20到钉钉"""
        try:
            print(f"🚀 获取最近{days}个交易日的策略股票实时行情...")

            # 获取策略股票
            stocks = self.get_recent_strategy_stocks(days=days)
            if not stocks:
                print("❌ 没有找到策略股票")
                return

            # 格式化股票代码
            formatted_codes = self.format_stock_codes(stocks)
            code_string = ','.join(formatted_codes)

            print(f"📊 获取 {len(stocks)} 个股票的实时行情...")

            # 获取实时数据
            realtime_data = self.get_tencent_price(code_string)

            # 合并数据
            combined_data = []
            for i, rt_data in enumerate(realtime_data):
                if i < len(stocks):
                    strategy_info = stocks[i]

                    combined_data.append({
                        'stock_code': strategy_info['stock_code'],
                        'stock_name': strategy_info['stock_name'],
                        'concepts': strategy_info['concepts'],
                        'entry_date': strategy_info['entry_date'],
                        'entry_price': strategy_info['entry_price'],
                        'current_price': float(rt_data['close']) if rt_data['close'] else 0,
                        'change_pct': float(rt_data['change_pct']) if rt_data['change_pct'] else 0,
                        'entry_change_pct': ((float(rt_data['close']) if rt_data['close'] else 0) - strategy_info['entry_price']) / strategy_info['entry_price'] * 100 if strategy_info['entry_price'] != 0 else 0,
                    })

            # 按涨跌幅排序
            sorted_data = sorted(combined_data, key=lambda x: x['change_pct'], reverse=True)
            top20_data = sorted_data[:20]

            # 发送TOP20到钉钉
            self.send_top20_to_dingtalk(top20_data)

            print("✅ TOP20结果已发送到钉钉")

        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            print(f"❌ 处理失败: {str(e)}")
    
    def send_top20_to_dingtalk(self, top_stocks):
        """发送前20名股票到钉钉"""
        try:
            if not top_stocks:
                return

            # 构建消息内容
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            message_content = f"## TOP20 ({current_time})\n\n"

            for i, stock in enumerate(top_stocks, 1):
                # 格式化涨跌幅
                change_pct = stock['change_pct']
                if change_pct > 0:
                    change_str = f"+{change_pct:.2f}%"
                elif change_pct < 0:
                    change_str = f"{change_pct:.2f}%"
                else:
                    change_str = f"0.00%"

                # 截断概念字符串
                concepts = stock['concepts'][:15] + "..." if len(stock['concepts']) > 15 else stock['concepts']

                # 清晰的两行格式 - 去掉数字格式化中的空格
                message_content += f"**{i}. {stock['stock_code']}  {stock['stock_name'].strip()}**\n"
                message_content += f"{stock['current_price']:.2f} || {change_str} || {stock['entry_change_pct']:.2f}% \n\n   {stock['entry_date']}   {concepts}\n\n"

            # 添加统计信息
            positive_count = len([x for x in top_stocks if x['change_pct'] > 0])
            avg_change = sum(x['change_pct'] for x in top_stocks) / len(top_stocks)
            max_gain = max(x['change_pct'] for x in top_stocks)

            # 统计概念涨幅
            concept_stats = {}
            for stock in top_stocks:
                concepts = stock['concepts'].split('、')
                for concept in concepts:
                    if concept not in concept_stats:
                        concept_stats[concept] = {'count': 0, 'total_change': 0}
                    concept_stats[concept]['count'] += 1
                    concept_stats[concept]['total_change'] += stock['change_pct']

            # 计算概念平均涨幅并排序（仅保留出现3次及以上的概念）
            concept_avg_changes = []
            for concept, stats in concept_stats.items():
                if stats['count'] >= 3:
                    avg_concept_change = stats['total_change'] / stats['count']
                    concept_avg_changes.append((concept, avg_concept_change))

            # 按平均涨幅排序，取前3个
            concept_avg_changes.sort(key=lambda x: x[1], reverse=True)
            top_concepts = concept_avg_changes[:3]

            # 构建概念统计消息
            concept_message = ""
            for i, (concept, avg) in enumerate(top_concepts, 1):
                # 获取概念出现次数
                count = concept_stats[concept]['count']
                concept_message += f"{concept}({count}) {avg:.2f}%"
                if i < len(top_concepts):
                    concept_message += " || "

            message_content += f"**统计**: 上涨{positive_count}/{len(top_stocks)}个  "
            message_content += f"平均{avg_change:.2f}% 最高{max_gain:.2f}%<br>"
            message_content += f"**概念**: {concept_message}" 
            
            # 发送钉钉消息
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": "策略股票实时监控 TOP20",
                    "text": message_content
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=message,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info("TOP20结果已发送到钉钉")
                else:
                    logger.warning(f"钉钉消息发送失败: {result}")
            else:
                logger.warning(f"钉钉请求失败: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"发送钉钉消息异常: {str(e)}")

def main():
    """主函数"""
    import sys
    
    try:
        # 解析命令行参数
        days = 15  # 默认15天
        if len(sys.argv) > 1:
            try:
                days = int(sys.argv[1])
                if days <= 0:
                    print("❌ 天数必须大于0")
                    return
            except ValueError:
                print("❌ 请输入有效的天数")
                return
        
        sender = Top20DingTalkSender()
        sender.get_realtime_data_and_send_top20(days=days)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")

if __name__ == '__main__':
    main()
