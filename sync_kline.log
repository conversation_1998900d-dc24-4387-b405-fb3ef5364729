2025-09-26 09:55:14,506 - __main__ - INFO - 📊 开始分析热门概念...
2025-09-26 09:55:14,506 - __main__ - ERROR - 分析热门概念失败: (pymysql.err.ProgrammingError) (1146, "Table 'choice.stock_daily_data' doesn't exist")
[SQL: 
                SELECT sector 
                FROM stock_daily_data 
                WHERE sector IS NOT NULL AND sector != ''
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-26 09:56:01,899 - sync_kline_by_concepts_chgip - INFO - 🌐 正在获取免费代理列表...
2025-09-26 09:56:13,131 - sync_kline_by_concepts_chgip - WARNING - 从 https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all 获取代理失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x136735e10>, 'Connection to api.proxyscrape.com timed out. (connect timeout=10)'))
2025-09-26 09:56:24,431 - sync_kline_by_concepts_chgip - WARNING - 从 https://raw.githubusercontent.com/TheSpeedX/SOCKS-List/master/http.txt 获取代理失败: HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=10)
2025-09-26 09:56:34,710 - sync_kline_by_concepts_chgip - WARNING - 从 https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt 获取代理失败: HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=10)
2025-09-26 09:56:34,710 - sync_kline_by_concepts_chgip - INFO - ✅ 成功获取 14 个免费代理
2025-09-26 09:56:34,710 - sync_kline_by_concepts_chgip - INFO - 🌐 正在获取免费代理列表...
2025-09-26 09:56:45,885 - sync_kline_by_concepts_chgip - WARNING - 从 https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all 获取代理失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x136758e90>, 'Connection to api.proxyscrape.com timed out. (connect timeout=10)'))
2025-09-26 09:56:56,274 - sync_kline_by_concepts_chgip - WARNING - 从 https://raw.githubusercontent.com/TheSpeedX/SOCKS-List/master/http.txt 获取代理失败: HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=10)
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - WARNING - 从 https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt 获取代理失败: HTTPSConnectionPool(host='raw.githubusercontent.com', port=443): Read timed out. (read timeout=10)
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - INFO - ✅ 成功获取 14 个免费代理
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - INFO - 🔄 代理池已更新，当前可用代理数: 14
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - INFO - 🔄 切换到代理 2/14
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - INFO - 🔄 切换到代理 3/14
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - INFO - 🔄 切换到代理 4/14
2025-09-26 09:57:06,984 - sync_kline_by_concepts_chgip - WARNING - ❌ 代理 http://************:3256 已标记为失败并从池中移除
2025-09-26 09:59:53,911 - __main__ - INFO - 🌐 正在获取免费代理列表...
2025-09-26 10:00:05,020 - __main__ - WARNING - 从 https://api.proxyscrape.com/v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all 获取代理失败: HTTPSConnectionPool(host='api.proxyscrape.com', port=443): Max retries exceeded with url: /v2/?request=get&protocol=http&timeout=10000&country=all&ssl=all&anonymity=all (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x12be6dcd0>, 'Connection to api.proxyscrape.com timed out. (connect timeout=10)'))
