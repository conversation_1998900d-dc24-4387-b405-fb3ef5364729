#!/usr/bin/env python3
"""
新策略回测功能
基于 new_strategy_results 表的结果进行回测
策略：入选当天收盘价买入，持有10个交易日后卖出
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy_backtest.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
engine = create_engine(Config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class StrategyBacktest:
    """策略回测器"""
    
    def __init__(self, holding_days=10):
        self.session = SessionLocal()
        self.holding_days = holding_days  # 持有天数
        
    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()
    
    def get_strategy_results(self):
        """获取策略结果"""
        try:
            query = text("""
                SELECT stock_code, stock_name, concepts, entry_date, entry_price,
                       previous_high_price, lowest_point_price, pullback_ratio,
                       distance_to_high, time_gap_days, created_at
                FROM new_strategy_results 
                ORDER BY entry_date DESC
            """)
            
            result = self.session.execute(query)
            rows = result.fetchall()
            
            if not rows:
                logger.warning("策略结果表中没有数据")
                return pd.DataFrame()
            
            # 转换为DataFrame
            df = pd.DataFrame(rows, columns=[
                'stock_code', 'stock_name', 'concepts', 'entry_date', 'entry_price',
                'previous_high_price', 'lowest_point_price', 'pullback_ratio',
                'distance_to_high', 'time_gap_days', 'created_at'
            ])
            
            logger.info(f"获取到 {len(df)} 个策略信号")
            return df
            
        except Exception as e:
            logger.error(f"获取策略结果失败: {str(e)}")
            return pd.DataFrame()
    
    def get_stock_price_data(self, stock_code, start_date, end_date):
        """获取股票价格数据"""
        try:
            query = text("""
                SELECT trade_date, open_price, high_price, low_price, close_price, volume
                FROM stock_kline_data 
                WHERE stock_code = :stock_code 
                AND trade_date >= :start_date 
                AND trade_date <= :end_date
                ORDER BY trade_date ASC
            """)
            
            result = self.session.execute(query, {
                'stock_code': stock_code,
                'start_date': start_date,
                'end_date': end_date
            })
            
            rows = result.fetchall()
            if not rows:
                return pd.DataFrame()
            
            df = pd.DataFrame(rows, columns=[
                'trade_date', 'open_price', 'high_price', 'low_price', 'close_price', 'volume'
            ])
            
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 价格数据失败: {str(e)}")
            return pd.DataFrame()
    
    def calculate_holding_return(self, stock_code, entry_date, entry_price):
        """计算持有收益"""
        try:
            # 计算查询的结束日期（入选日期后30个交易日，确保有足够数据）
            end_date = entry_date + timedelta(days=50)

            # 获取价格数据
            price_df = self.get_stock_price_data(stock_code, entry_date, end_date)

            if price_df.empty:
                logger.warning(f"股票 {stock_code} 在 {entry_date} 后没有价格数据")
                return None

            logger.debug(f"股票 {stock_code} 获取到 {len(price_df)} 天的价格数据")
            
            # 找到入选日期当天的数据
            entry_day_data = price_df[price_df['trade_date'].dt.date == entry_date]

            if entry_day_data.empty:
                # 如果入选日期当天没有数据，尝试找到最近的交易日
                logger.warning(f"股票 {stock_code} 在入选日期 {entry_date} 没有交易数据，尝试找最近交易日")

                # 找到入选日期之后的第一个交易日
                future_data = price_df[price_df['trade_date'].dt.date > entry_date]
                if future_data.empty:
                    logger.warning(f"股票 {stock_code} 在入选日期 {entry_date} 后没有交易数据")
                    return None

                entry_day_data = future_data.head(1)
                actual_entry_date = entry_day_data.iloc[0]['trade_date'].date()
                logger.info(f"使用 {actual_entry_date} 作为实际买入日期")
            else:
                actual_entry_date = entry_date
            
            # 获取入选日期当天的收盘价作为买入价格
            buy_price = entry_day_data.iloc[0]['close_price']
            
            # 找到实际买入日期在价格数据中的位置
            entry_idx = price_df[price_df['trade_date'].dt.date == actual_entry_date].index[0]
            
            # 计算持有期结束的位置（入选日期后第N个交易日）
            sell_idx = entry_idx + self.holding_days
            
            if sell_idx >= len(price_df):
                logger.warning(f"股票 {stock_code} 持有期数据不足，只有 {len(price_df) - entry_idx - 1} 个交易日")
                # 使用最后一个交易日的数据
                sell_idx = len(price_df) - 1
                actual_holding_days = sell_idx - entry_idx
            else:
                actual_holding_days = self.holding_days
            
            # 获取卖出价格（持有期结束当天的收盘价）
            sell_price = price_df.iloc[sell_idx]['close_price']
            sell_date = price_df.iloc[sell_idx]['trade_date'].date()
            
            # 计算收益率
            return_rate = (sell_price - buy_price) / buy_price * 100
            
            # 计算持有期间的最高价和最低价
            holding_period_data = price_df.iloc[entry_idx:sell_idx+1]
            max_price = holding_period_data['high_price'].max()
            min_price = holding_period_data['low_price'].min()
            
            # 计算最大收益和最大亏损
            max_return = (max_price - buy_price) / buy_price * 100
            max_loss = (min_price - buy_price) / buy_price * 100
            
            return {
                'stock_code': stock_code,
                'entry_date': entry_date,
                'sell_date': sell_date,
                'buy_price': buy_price,
                'sell_price': sell_price,
                'return_rate': return_rate,
                'actual_holding_days': actual_holding_days,
                'max_price': max_price,
                'min_price': min_price,
                'max_return': max_return,
                'max_loss': max_loss
            }
            
        except Exception as e:
            logger.error(f"计算股票 {stock_code} 持有收益失败: {str(e)}")
            return None
    
    def run_backtest(self):
        """运行回测"""
        try:
            print("🚀 开始策略回测")
            print(f"📊 回测参数：持有期 {self.holding_days} 个交易日")
            print("=" * 80)
            
            # 获取策略结果
            strategy_df = self.get_strategy_results()
            
            if strategy_df.empty:
                print("❌ 没有策略结果数据")
                return
            
            print(f"📈 策略信号总数: {len(strategy_df)}")
            
            # 回测结果列表
            backtest_results = []
            
            # 逐个计算每个信号的收益
            for i, row in strategy_df.iterrows():
                stock_code = row['stock_code']
                stock_name = row['stock_name']
                entry_date = row['entry_date']
                entry_price = row['entry_price']
                
                logger.info(f"[{i+1}/{len(strategy_df)}] 回测股票: {stock_code} - {stock_name}")
                
                # 计算持有收益
                result = self.calculate_holding_return(stock_code, entry_date, entry_price)
                
                if result:
                    result['stock_name'] = stock_name
                    result['concepts'] = row['concepts']
                    result['strategy_entry_price'] = entry_price  # 策略记录的入选价格
                    backtest_results.append(result)
                    
                    print(f"✅ {stock_code} - {stock_name}")
                    print(f"   入选: {entry_date} 买入: {result['buy_price']:.2f}")
                    print(f"   卖出: {result['sell_date']} 卖出: {result['sell_price']:.2f}")
                    print(f"   收益: {result['return_rate']:.2f}% (持有{result['actual_holding_days']}天)")
                else:
                    print(f"❌ {stock_code} - {stock_name} 回测失败")
            
            if not backtest_results:
                print("❌ 没有成功的回测结果")
                return
            
            # 生成回测报告
            self.generate_backtest_report(backtest_results)
            
        except Exception as e:
            logger.error(f"回测执行失败: {str(e)}")
            print(f"❌ 回测执行失败: {str(e)}")
    
    def generate_backtest_report(self, results):
        """生成回测报告"""
        try:
            results_df = pd.DataFrame(results)
            
            print("\n" + "=" * 80)
            print("📊 回测报告")
            print("=" * 80)
            
            # 基本统计
            total_trades = len(results_df)
            profitable_trades = len(results_df[results_df['return_rate'] > 0])
            loss_trades = len(results_df[results_df['return_rate'] < 0])
            
            print(f"📈 交易统计:")
            print(f"   总交易次数: {total_trades}")
            print(f"   盈利次数: {profitable_trades} ({profitable_trades/total_trades*100:.1f}%)")
            print(f"   亏损次数: {loss_trades} ({loss_trades/total_trades*100:.1f}%)")
            
            # 收益统计
            avg_return = results_df['return_rate'].mean()
            median_return = results_df['return_rate'].median()
            max_return = results_df['return_rate'].max()
            min_return = results_df['return_rate'].min()
            std_return = results_df['return_rate'].std()
            
            print(f"\n📊 收益统计:")
            print(f"   平均收益率: {avg_return:.2f}%")
            print(f"   中位数收益率: {median_return:.2f}%")
            print(f"   最大收益率: {max_return:.2f}%")
            print(f"   最大亏损率: {min_return:.2f}%")
            print(f"   收益率标准差: {std_return:.2f}%")
            
            # 持有期统计
            avg_holding_days = results_df['actual_holding_days'].mean()
            print(f"\n📅 持有期统计:")
            print(f"   平均持有天数: {avg_holding_days:.1f} 天")
            
            # 最佳和最差表现
            best_trade = results_df.loc[results_df['return_rate'].idxmax()]
            worst_trade = results_df.loc[results_df['return_rate'].idxmin()]
            
            print(f"\n🏆 最佳表现:")
            print(f"   股票: {best_trade['stock_code']} - {best_trade['stock_name']}")
            print(f"   收益率: {best_trade['return_rate']:.2f}%")
            print(f"   交易期间: {best_trade['entry_date']} 到 {best_trade['sell_date']}")
            
            print(f"\n📉 最差表现:")
            print(f"   股票: {worst_trade['stock_code']} - {worst_trade['stock_name']}")
            print(f"   收益率: {worst_trade['return_rate']:.2f}%")
            print(f"   交易期间: {worst_trade['entry_date']} 到 {worst_trade['sell_date']}")
            
            # 收益率分布
            print(f"\n📈 收益率分布:")
            bins = [-float('inf'), -10, -5, 0, 5, 10, 20, float('inf')]
            labels = ['<-10%', '-10%~-5%', '-5%~0%', '0%~5%', '5%~10%', '10%~20%', '>20%']
            
            for i, label in enumerate(labels):
                if i == 0:
                    count = len(results_df[results_df['return_rate'] < bins[i+1]])
                elif i == len(labels) - 1:
                    count = len(results_df[results_df['return_rate'] >= bins[i]])
                else:
                    count = len(results_df[(results_df['return_rate'] >= bins[i]) & 
                                         (results_df['return_rate'] < bins[i+1])])
                
                percentage = count / total_trades * 100
                print(f"   {label}: {count} 次 ({percentage:.1f}%)")
            
            print("=" * 80)
            
        except Exception as e:
            logger.error(f"生成回测报告失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 新策略回测系统启动")
    
    try:
        # 创建回测器（默认持有10个交易日）
        backtest = StrategyBacktest(holding_days=10)
        
        # 运行回测
        backtest.run_backtest()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        logger.error(f"详细错误: {str(e)}")

if __name__ == '__main__':
    main()
