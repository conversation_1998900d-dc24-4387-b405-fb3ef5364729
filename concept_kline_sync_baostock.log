2025-09-29 10:02:54,699 - sync_kline_by_concepts_baostock - ERROR - ❌ baostock登录失败: 客户端版本不匹配，请使用'pip install baostock --upgrade'更新至下列版本00.8.90|00.9.00
2025-09-29 10:02:54,706 - sync_kline_by_concepts_baostock - INFO - 📊 开始分析热门概念...
2025-09-29 10:02:54,707 - sync_kline_by_concepts_baostock - ERROR - 分析热门概念失败: (pymysql.err.ProgrammingError) (1146, "Table 'choice.stock_daily_data' doesn't exist")
[SQL: 
                SELECT sector
                FROM stock_daily_data
                WHERE sector IS NOT NULL AND sector != ''
            ]
(Background on this error at: https://sqlalche.me/e/20/f405)
2025-09-29 10:10:07,423 - sync_kline_by_concepts_baostock - INFO - ✅ baostock登录成功
2025-09-29 10:10:07,423 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000001 (sz.000001) 的K线数据，时间范围: 2024-01-01 到 2025-09-29
2025-09-29 10:10:07,569 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取到 423 条数据
2025-09-29 10:10:08,066 - sync_kline_by_concepts_baostock - INFO - 📊 从现有数据中获取股票列表...
2025-09-29 10:10:08,068 - sync_kline_by_concepts_baostock - INFO - 现有数据中没有找到股票，使用默认列表
2025-09-29 10:10:08,068 - sync_kline_by_concepts_baostock - INFO - 📊 获取默认股票列表...
2025-09-29 10:10:08,068 - sync_kline_by_concepts_baostock - INFO - 默认股票列表包含 22 个股票
2025-09-29 10:10:43,404 - __main__ - INFO - 📊 从现有数据中获取股票列表...
2025-09-29 10:10:43,405 - __main__ - INFO - 现有数据中没有找到股票，使用默认列表
2025-09-29 10:10:43,405 - __main__ - INFO - 📊 获取默认股票列表...
2025-09-29 10:10:43,405 - __main__ - INFO - 默认股票列表包含 22 个股票
2025-09-29 10:12:28,307 - sync_kline_by_concepts_baostock - INFO - 🗓️ 新的一天 2025-09-29，清空K线数据表...
2025-09-29 10:12:28,309 - sync_kline_by_concepts_baostock - INFO - ✅ 清空了 0 条K线数据
2025-09-29 10:12:28,312 - sync_kline_by_concepts_baostock - INFO - 📊 创建新的同步进度记录，总股票数: 3
2025-09-29 10:12:28,547 - sync_kline_by_concepts_baostock - INFO - ✅ baostock登录成功
2025-09-29 10:12:28,547 - sync_kline_by_concepts_baostock - INFO - 🚀 开始同步K线数据，起始日期: 2025-08-30
2025-09-29 10:12:28,547 - sync_kline_by_concepts_baostock - INFO - 📊 总股票数: 3, 剩余: 3
2025-09-29 10:12:28,550 - sync_kline_by_concepts_baostock - INFO - 📊 已完成: 0, 已失败: 0
2025-09-29 10:12:28,550 - sync_kline_by_concepts_baostock - INFO - [1/3] 正在获取股票 000001 的K线数据...
2025-09-29 10:12:28,550 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000001 (sz.000001) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:28,658 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取到 20 条数据
2025-09-29 10:12:28,668 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000001 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000001', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 12.05, 'high_price': 12.05, 'low_price': 11.85, 'close_price': 11.89, 'volume': 185845902.0, 'amount': 2214306331.71, 'turnover_rate': 0.9577}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:28,668 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000001 获取成功但保存失败
2025-09-29 10:12:28,668 - sync_kline_by_concepts_baostock - INFO - [1/3] 重试第 1 次获取股票 000001 的K线数据...
2025-09-29 10:12:28,668 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000001 (sz.000001) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:28,764 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取到 20 条数据
2025-09-29 10:12:28,770 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000001 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000001', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 12.05, 'high_price': 12.05, 'low_price': 11.85, 'close_price': 11.89, 'volume': 185845902.0, 'amount': 2214306331.71, 'turnover_rate': 0.9577}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:28,770 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000001 获取成功但保存失败
2025-09-29 10:12:28,770 - sync_kline_by_concepts_baostock - INFO - [1/3] 重试第 2 次获取股票 000001 的K线数据...
2025-09-29 10:12:28,770 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000001 (sz.000001) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:28,854 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取到 20 条数据
2025-09-29 10:12:28,859 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000001 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000001', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 12.05, 'high_price': 12.05, 'low_price': 11.85, 'close_price': 11.89, 'volume': 185845902.0, 'amount': 2214306331.71, 'turnover_rate': 0.9577}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:28,860 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000001 获取成功但保存失败
2025-09-29 10:12:28,864 - sync_kline_by_concepts_baostock - ERROR - ❌ 股票 000001 经过 3 次重试后仍然失败
2025-09-29 10:12:28,865 - sync_kline_by_concepts_baostock - INFO - [3/3] 正在获取股票 600036 的K线数据...
2025-09-29 10:12:28,865 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 600036 (sh.600036) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:28,969 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 获取到 20 条数据
2025-09-29 10:12:28,975 - sync_kline_by_concepts_baostock - ERROR - 保存股票 600036 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '600036', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 42.62, 'high_price': 42.64, 'low_price': 41.88, 'close_price': 41.98, 'volume': 127633329.0, 'amount': 5381676512.78, 'turnover_rate': 0.6187}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:28,975 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 600036 获取成功但保存失败
2025-09-29 10:12:28,976 - sync_kline_by_concepts_baostock - INFO - [3/3] 重试第 1 次获取股票 600036 的K线数据...
2025-09-29 10:12:28,976 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 600036 (sh.600036) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:29,465 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 获取到 20 条数据
2025-09-29 10:12:29,472 - sync_kline_by_concepts_baostock - ERROR - 保存股票 600036 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '600036', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 42.62, 'high_price': 42.64, 'low_price': 41.88, 'close_price': 41.98, 'volume': 127633329.0, 'amount': 5381676512.78, 'turnover_rate': 0.6187}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:29,472 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 600036 获取成功但保存失败
2025-09-29 10:12:29,472 - sync_kline_by_concepts_baostock - INFO - [3/3] 重试第 2 次获取股票 600036 的K线数据...
2025-09-29 10:12:29,472 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 600036 (sh.600036) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:29,556 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 获取到 20 条数据
2025-09-29 10:12:29,563 - sync_kline_by_concepts_baostock - ERROR - 保存股票 600036 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '600036', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 42.62, 'high_price': 42.64, 'low_price': 41.88, 'close_price': 41.98, 'volume': 127633329.0, 'amount': 5381676512.78, 'turnover_rate': 0.6187}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:29,563 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 600036 获取成功但保存失败
2025-09-29 10:12:29,566 - sync_kline_by_concepts_baostock - ERROR - ❌ 股票 600036 经过 3 次重试后仍然失败
2025-09-29 10:12:29,568 - sync_kline_by_concepts_baostock - INFO - [5/3] 正在获取股票 000002 的K线数据...
2025-09-29 10:12:29,568 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000002 (sz.000002) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:29,684 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 获取到 20 条数据
2025-09-29 10:12:29,690 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000002 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000002', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 6.76, 'high_price': 6.86, 'low_price': 6.7, 'close_price': 6.81, 'volume': 140682543.0, 'amount': 953846377.4, 'turnover_rate': 1.4479}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:29,690 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000002 获取成功但保存失败
2025-09-29 10:12:29,690 - sync_kline_by_concepts_baostock - INFO - [5/3] 重试第 1 次获取股票 000002 的K线数据...
2025-09-29 10:12:29,690 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000002 (sz.000002) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:29,816 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 获取到 20 条数据
2025-09-29 10:12:29,823 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000002 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000002', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 6.76, 'high_price': 6.86, 'low_price': 6.7, 'close_price': 6.81, 'volume': 140682543.0, 'amount': 953846377.4, 'turnover_rate': 1.4479}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:29,823 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000002 获取成功但保存失败
2025-09-29 10:12:29,823 - sync_kline_by_concepts_baostock - INFO - [5/3] 重试第 2 次获取股票 000002 的K线数据...
2025-09-29 10:12:29,823 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000002 (sz.000002) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:12:29,938 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 获取到 20 条数据
2025-09-29 10:12:29,945 - sync_kline_by_concepts_baostock - ERROR - 保存股票 000002 K线数据失败: (pymysql.err.OperationalError) (1364, "Field 'stock_name' doesn't have a default value")
[SQL: INSERT INTO stock_kline_data (stock_code, trade_date, open_price, high_price, low_price, close_price, volume, amount, turnover_rate) VALUES (%(stock_code)s, %(trade_date)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(amount)s, %(turnover_rate)s)]
[parameters: {'stock_code': '000002', 'trade_date': datetime.date(2025, 9, 1), 'open_price': 6.76, 'high_price': 6.86, 'low_price': 6.7, 'close_price': 6.81, 'volume': 140682543.0, 'amount': 953846377.4, 'turnover_rate': 1.4479}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-09-29 10:12:29,945 - sync_kline_by_concepts_baostock - WARNING - ⚠️ 股票 000002 获取成功但保存失败
2025-09-29 10:12:29,946 - sync_kline_by_concepts_baostock - INFO - 🎉 同步完成！成功: 0, 失败: 3
2025-09-29 10:12:29,949 - sync_kline_by_concepts_baostock - ERROR - ❌ 股票 000002 经过 3 次重试后仍然失败
2025-09-29 10:12:29,952 - sync_kline_by_concepts_baostock - INFO - 🎉 本次同步完成！本次成功: 0, 本次失败: 3
2025-09-29 10:12:29,952 - sync_kline_by_concepts_baostock - INFO - 📊 总体进度: 成功: 0, 失败: 6, 总计: 3
2025-09-29 10:12:29,988 - sync_kline_by_concepts_baostock - INFO - ✅ baostock已登出
2025-09-29 10:13:14,561 - sync_kline_by_concepts_baostock - INFO - 📅 今天 2025-09-29 已有同步记录，继续之前的进度
2025-09-29 10:13:14,561 - sync_kline_by_concepts_baostock - INFO - 📊 剩余需要同步的股票: 0/3
2025-09-29 10:13:14,561 - sync_kline_by_concepts_baostock - INFO - ✅ 所有股票已同步完成！
2025-09-29 10:13:41,157 - sync_kline_by_concepts_baostock - INFO - 🗓️ 新的一天 2025-09-29，清空K线数据表...
2025-09-29 10:13:41,158 - sync_kline_by_concepts_baostock - INFO - ✅ 清空了 0 条K线数据
2025-09-29 10:13:41,162 - sync_kline_by_concepts_baostock - INFO - 📊 创建新的同步进度记录，总股票数: 3
2025-09-29 10:13:41,607 - sync_kline_by_concepts_baostock - INFO - ✅ baostock登录成功
2025-09-29 10:13:41,608 - sync_kline_by_concepts_baostock - INFO - 🚀 开始同步K线数据，起始日期: 2025-08-30
2025-09-29 10:13:41,608 - sync_kline_by_concepts_baostock - INFO - 📊 总股票数: 3, 剩余: 3
2025-09-29 10:13:41,611 - sync_kline_by_concepts_baostock - INFO - 📊 已完成: 0, 已失败: 0
2025-09-29 10:13:41,611 - sync_kline_by_concepts_baostock - INFO - [1/3] 正在获取股票 000001 的K线数据...
2025-09-29 10:13:41,611 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000001 (sz.000001) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:13:41,697 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取到 20 条数据
2025-09-29 10:13:41,715 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 保存成功，共 20 条数据
2025-09-29 10:13:41,715 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000001 获取并保存成功，数据量: 20
2025-09-29 10:13:43,723 - sync_kline_by_concepts_baostock - INFO - [3/3] 正在获取股票 600036 的K线数据...
2025-09-29 10:13:43,723 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 600036 (sh.600036) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:13:44,208 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 获取到 20 条数据
2025-09-29 10:13:44,222 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 保存成功，共 20 条数据
2025-09-29 10:13:44,223 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 600036 获取并保存成功，数据量: 20
2025-09-29 10:13:46,012 - sync_kline_by_concepts_baostock - INFO - [5/3] 正在获取股票 000002 的K线数据...
2025-09-29 10:13:46,012 - sync_kline_by_concepts_baostock - INFO - 正在获取股票 000002 (sz.000002) 的K线数据，时间范围: 2025-08-30 到 2025-09-29
2025-09-29 10:13:46,603 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 获取到 20 条数据
2025-09-29 10:13:46,617 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 保存成功，共 20 条数据
2025-09-29 10:13:46,618 - sync_kline_by_concepts_baostock - INFO - ✅ 股票 000002 获取并保存成功，数据量: 20
2025-09-29 10:13:46,619 - sync_kline_by_concepts_baostock - INFO - 🎉 同步完成！成功: 3, 失败: 0
2025-09-29 10:13:47,742 - sync_kline_by_concepts_baostock - INFO - 🎉 本次同步完成！本次成功: 3, 本次失败: 0
2025-09-29 10:13:47,742 - sync_kline_by_concepts_baostock - INFO - 📊 总体进度: 成功: 6, 失败: 0, 总计: 3
2025-09-29 10:13:47,805 - sync_kline_by_concepts_baostock - INFO - ✅ baostock已登出
2025-09-29 20:43:29,715 - __main__ - INFO - 📊 从现有数据中获取股票列表...
2025-09-29 20:43:29,721 - __main__ - INFO - 从现有数据中找到 2 个股票
2025-09-29 20:43:29,730 - __main__ - INFO - 📅 今天 2025-09-29 已有同步记录，继续之前的进度
2025-09-29 20:43:29,736 - __main__ - INFO - 📊 剩余需要同步的股票: 0/2
2025-09-29 20:43:29,736 - __main__ - INFO - ✅ 所有股票已同步完成！
