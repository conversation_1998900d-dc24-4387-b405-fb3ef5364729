#!/bin/bash

# 股票TOP20监控系统启动脚本 - 前后端分离版本

echo "🚀 启动股票TOP20监控系统 (前后端分离版本)"
echo "=" * 60

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在，请先创建虚拟环境"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

# 检查依赖
echo "📦 检查依赖..."
python3 -c "import fastapi, uvicorn, passlib" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少必要依赖，请运行以下命令安装："
    echo "pip install fastapi uvicorn passlib[bcrypt] python-jose python-multipart"
    exit 1
fi

# 检查数据库表
echo "🗄️ 检查数据库..."
python3 -c "
from sqlalchemy import create_engine, text
from config import Config
try:
    engine = create_engine(Config.DATABASE_URL)
    with engine.connect() as conn:
        result = conn.execute(text('SHOW TABLES LIKE \"top20_cache\"'))
        if not result.fetchone():
            print('❌ 数据库表不存在，请先运行: python3 init_web_database.py')
            exit(1)
        else:
            print('✅ 数据库连接正常')
except Exception as e:
    print(f'❌ 数据库连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    exit 1
fi

# 停止可能运行的旧进程
echo "🛑 停止旧进程..."
pkill -f "backend_api:app" 2>/dev/null || true
pkill -f "frontend_server:app" 2>/dev/null || true
sleep 2

# 启动后端API服务器
echo "🔧 启动后端API服务器..."
nohup python3 -m uvicorn backend_api:app --host 127.0.0.1 --port 8000 > backend.log 2>&1 &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 检查后端是否启动成功
curl -s http://127.0.0.1:8000/api/health > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 后端API服务器启动成功 (PID: $BACKEND_PID)"
else
    echo "❌ 后端API服务器启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务器
echo "🌐 启动前端服务器..."
nohup python3 -m uvicorn frontend_server:app --host 127.0.0.1 --port 3000 > frontend.log 2>&1 &
FRONTEND_PID=$!

# 等待前端启动
sleep 3

# 检查前端是否启动成功
curl -s http://127.0.0.1:3000 > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 前端服务器启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端服务器启动失败"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 系统启动成功！"
echo "=" * 60
echo "🌐 前端地址: http://127.0.0.1:3000"
echo "🔧 后端API: http://127.0.0.1:8000"
echo "📚 API文档: http://127.0.0.1:8000/docs"
echo ""
echo "👤 测试账号:"
echo "   管理员 - 用户名: admin, 密码: admin123"
echo "   普通用户 - 用户名: user, 密码: user123"
echo ""
echo "📋 进程信息:"
echo "   后端PID: $BACKEND_PID"
echo "   前端PID: $FRONTEND_PID"
echo ""
echo "📄 日志文件:"
echo "   后端日志: backend.log"
echo "   前端日志: frontend.log"
echo ""
echo "🛑 停止服务: ./stop_frontend_backend.sh"
echo "=" * 60

# 保存PID到文件
echo $BACKEND_PID > backend.pid
echo $FRONTEND_PID > frontend.pid

echo "✨ 请访问 http://127.0.0.1:3000 开始使用系统"
