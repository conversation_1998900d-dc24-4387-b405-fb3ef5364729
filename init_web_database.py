#!/usr/bin/env python3
"""
初始化Web应用所需的数据库表
"""

import logging
from sqlalchemy import create_engine, text
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_top20_cache_table():
    """创建TOP20缓存表"""
    try:
        engine = create_engine(Config.DATABASE_URL)
        
        with engine.connect() as conn:
            # 创建TOP20缓存表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS top20_cache (
                id INT AUTO_INCREMENT PRIMARY KEY,
                rank_num INT NOT NULL COMMENT '排名',
                stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
                stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
                current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
                change_pct DECIMAL(8,2) NOT NULL COMMENT '涨跌幅(%)',
                entry_date DATE NOT NULL COMMENT '入选日期',
                concepts TEXT COMMENT '概念',
                entry_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '入选价格',
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_rank (rank_num),
                INDEX idx_stock_code (stock_code),
                INDEX idx_update_time (update_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='TOP20股票缓存表';
            """
            
            conn.execute(text(create_table_sql))
            conn.commit()
            logger.info("✅ TOP20缓存表创建成功")
            
            # 检查表是否存在
            result = conn.execute(text("SHOW TABLES LIKE 'top20_cache'"))
            if result.fetchone():
                logger.info("✅ 表 top20_cache 已存在")
            else:
                logger.error("❌ 表 top20_cache 创建失败")
                return False
                
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建TOP20缓存表失败: {str(e)}")
        return False

def create_user_sessions_table():
    """创建用户会话表（可选，用于更复杂的用户管理）"""
    try:
        engine = create_engine(Config.DATABASE_URL)
        
        with engine.connect() as conn:
            # 创建用户会话表
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL COMMENT '用户名',
                session_token VARCHAR(255) NOT NULL COMMENT '会话令牌',
                login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
                ip_address VARCHAR(45) COMMENT 'IP地址',
                user_agent TEXT COMMENT '用户代理',
                is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
                INDEX idx_username (username),
                INDEX idx_session_token (session_token),
                INDEX idx_last_activity (last_activity)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户会话表';
            """
            
            conn.execute(text(create_table_sql))
            conn.commit()
            logger.info("✅ 用户会话表创建成功")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建用户会话表失败: {str(e)}")
        return False

def insert_sample_data():
    """插入示例数据"""
    try:
        engine = create_engine(Config.DATABASE_URL)
        
        with engine.connect() as conn:
            # 检查是否已有数据
            result = conn.execute(text("SELECT COUNT(*) FROM top20_cache"))
            count = result.fetchone()[0]
            
            if count > 0:
                logger.info(f"TOP20缓存表已有 {count} 条数据，跳过插入示例数据")
                return True
            
            # 插入示例数据
            sample_data = [
                (1, '000001', '平安银行', 12.50, 2.45, '2025-08-01', '银行、金融科技', 12.20),
                (2, '000002', '万科A', 8.90, 1.85, '2025-08-01', '房地产、物业管理', 8.74),
                (3, '600036', '招商银行', 35.60, 1.42, '2025-08-02', '银行、金融科技', 35.10),
                (4, '000858', '五粮液', 128.50, 0.95, '2025-08-02', '白酒、消费', 127.28),
                (5, '600519', '贵州茅台', 1680.00, 0.75, '2025-08-03', '白酒、消费', 1667.50),
            ]
            
            for data in sample_data:
                conn.execute(text("""
                    INSERT INTO top20_cache 
                    (rank_num, stock_code, stock_name, current_price, change_pct, 
                     entry_date, concepts, entry_price)
                    VALUES (:rank_num, :stock_code, :stock_name, :current_price, :change_pct,
                            :entry_date, :concepts, :entry_price)
                """), {
                    'rank_num': data[0],
                    'stock_code': data[1],
                    'stock_name': data[2],
                    'current_price': data[3],
                    'change_pct': data[4],
                    'entry_date': data[5],
                    'concepts': data[6],
                    'entry_price': data[7]
                })
            
            conn.commit()
            logger.info(f"✅ 插入 {len(sample_data)} 条示例数据")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ 插入示例数据失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始初始化Web应用数据库...")
    
    success = True
    
    # 创建TOP20缓存表
    if not create_top20_cache_table():
        success = False
    
    # 创建用户会话表
    if not create_user_sessions_table():
        success = False
    
    # 插入示例数据
    if not insert_sample_data():
        success = False
    
    if success:
        logger.info("✅ Web应用数据库初始化完成")
        print("\n" + "="*60)
        print("🎉 数据库初始化成功！")
        print("📊 已创建以下表:")
        print("   - top20_cache: TOP20股票缓存表")
        print("   - user_sessions: 用户会话表")
        print("🔗 现在可以启动Web应用:")
        print("   ./start_web_app.sh")
        print("   或者: python3 web_app.py")
        print("="*60)
    else:
        logger.error("❌ 数据库初始化失败")
        print("\n" + "="*60)
        print("❌ 数据库初始化失败！")
        print("请检查数据库连接和权限设置")
        print("="*60)

if __name__ == '__main__':
    main()
