# 配置文件
import os
from typing import List

class Config:
    """股票数据获取配置类"""
    
    # 数据库配置
    # DATABASE_URL = os.getenv('DATABASE_URL', 'mysql+pymysql://root:tanxi219.@localhost:3306/choice')
    DATABASE_URL = os.getenv('DATABASE_URL', 'mysql+pymysql://root:root@*************:3306/data_base')

    # API配置
    BASE_URL = "https://apphis.longhuvip.com/w1/api/index.php"
    
    # 请求配置
    REQUEST_TIMEOUT = 15  # 请求超时时间（秒）
    MAX_RETRIES = 3      # 最大重试次数
    
    # 防封IP配置
    MIN_DELAY = 5        # 最小延迟时间（秒）
    MAX_DELAY = 15       # 最大延迟时间（秒）
    RETRY_DELAY_MIN = 3  # 重试最小延迟时间（秒）
    RETRY_DELAY_MAX = 8  # 重试最大延迟时间（秒）
    RATE_LIMIT_DELAY_MIN = 10  # 遇到429错误时的最小延迟（秒）
    RATE_LIMIT_DELAY_MAX = 20  # 遇到429错误时的最大延迟（秒）
    
    # User-Agent池 - PostmanRuntime放在首位，因为测试证明它最有效
    USER_AGENTS: List[str] = [
        'PostmanRuntime/7.32.3',  # 最有效的User-Agent
        'PostmanRuntime/7.28.4',
        'PostmanRuntime/7.29.2',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
    ]
    
    # API参数配置
    API_PARAMS = {
        'Index': '0',
        'Order': '1',
        'PhoneOSNew': '2',
        'PidType': '1',
        'Type': '5',
        'VerSion': '********',
        'a': 'DailyLimitPerformance2',
        'apiv': 'w40',
        'c': 'HisHomeDingPan',
        'st': '1000'
    }
    
    # 请求头模板 - 模拟Postman的行为
    HEADERS_TEMPLATE = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive'
    }
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 数据验证配置
    MIN_STOCK_DATA_FIELDS = 17  # 股票数据最少字段数
    
    @classmethod
    def get_headers(cls, user_agent: str = None) -> dict:
        """获取请求头"""
        headers = cls.HEADERS_TEMPLATE.copy()
        if user_agent:
            headers['User-Agent'] = user_agent
        return headers
    
    @classmethod
    def get_api_params(cls, date_str: str) -> dict:
        """获取API参数"""
        params = cls.API_PARAMS.copy()
        params['Day'] = date_str
        return params
