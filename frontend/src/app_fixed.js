// 股票TOP20监控前端应用 - 修复版本
const { createApp, ref, reactive, onMounted, computed, h } = Vue;
const naive = window.naive;

// API配置
const API_BASE_URL = 'http://127.0.0.1:8000';

// API工具函数
const api = {
    baseURL: API_BASE_URL,
    token: localStorage.getItem('token'),
    
    setToken(token) {
        this.token = token;
        localStorage.setItem('token', token);
    },
    
    clearToken() {
        this.token = null;
        localStorage.removeItem('token');
    },
    
    async request(url, options = {}) {
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.token && { 'Authorization': `Bearer ${this.token}` })
            },
            ...options
        };
        
        try {
            const response = await fetch(this.baseURL + url, config);
            
            if (response.status === 401) {
                this.clearToken();
                throw new Error('登录已过期，请重新登录');
            }
            
            if (!response.ok) {
                const error = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(error.detail || '请求失败');
            }
            
            return response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('无法连接到服务器，请检查后端服务是否启动');
            }
            throw error;
        }
    },
    
    async login(username, password) {
        const response = await this.request('/api/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
        this.setToken(response.access_token);
        return response;
    },
    
    async getTop20() {
        return this.request('/api/top20');
    },
    
    async getUserInfo() {
        return this.request('/api/user/me');
    },
    
    async healthCheck() {
        return this.request('/api/health');
    }
};

// 主应用组件
const App = {
    setup() {
        const state = reactive({
            isLoggedIn: !!api.token,
            user: null,
            loading: false,
            top20Data: null,
            autoRefresh: true,
            refreshInterval: null,
            serverStatus: 'unknown'
        });
        
        const loginForm = reactive({
            username: '',
            password: '',
            loading: false
        });

        // 消息提示函数
        const showMessage = (content, type = 'info') => {
            console.log(`[${type.toUpperCase()}] ${content}`);
            // 简单的消息提示
            const messageEl = document.createElement('div');
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'error' ? '#f56565' : type === 'success' ? '#48bb78' : '#4299e1'};
                color: white;
                border-radius: 6px;
                z-index: 9999;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            messageEl.textContent = content;
            document.body.appendChild(messageEl);
            
            setTimeout(() => {
                document.body.removeChild(messageEl);
            }, 3000);
        };

        // 检查服务器状态
        const checkServerStatus = async () => {
            try {
                await api.healthCheck();
                state.serverStatus = 'healthy';
            } catch (error) {
                state.serverStatus = 'unhealthy';
                console.error('服务器健康检查失败:', error);
            }
        };

        // 登录
        const handleLogin = async () => {
            if (!loginForm.username || !loginForm.password) {
                showMessage('请输入用户名和密码', 'error');
                return;
            }
            
            loginForm.loading = true;
            try {
                await api.login(loginForm.username, loginForm.password);
                state.isLoggedIn = true;
                showMessage('登录成功', 'success');
                await loadUserInfo();
                await loadTop20Data();
                startAutoRefresh();
            } catch (error) {
                showMessage(error.message, 'error');
            } finally {
                loginForm.loading = false;
            }
        };

        // 登出
        const handleLogout = () => {
            if (confirm('确定要退出登录吗？')) {
                api.clearToken();
                state.isLoggedIn = false;
                state.user = null;
                state.top20Data = null;
                stopAutoRefresh();
                showMessage('已退出登录', 'success');
            }
        };

        // 加载用户信息
        const loadUserInfo = async () => {
            try {
                state.user = await api.getUserInfo();
            } catch (error) {
                console.error('加载用户信息失败:', error);
                showMessage('加载用户信息失败', 'error');
            }
        };

        // 加载TOP20数据
        const loadTop20Data = async () => {
            state.loading = true;
            try {
                state.top20Data = await api.getTop20();
                if (state.top20Data.stocks.length === 0) {
                    showMessage('暂无TOP20数据，请等待数据更新', 'info');
                }
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
                console.error('加载TOP20数据失败:', error);
            } finally {
                state.loading = false;
            }
        };

        // 自动刷新
        const startAutoRefresh = () => {
            if (state.refreshInterval) return;
            state.refreshInterval = setInterval(() => {
                if (state.autoRefresh && state.isLoggedIn) {
                    loadTop20Data();
                }
            }, 60000); // 每分钟刷新一次
        };

        const stopAutoRefresh = () => {
            if (state.refreshInterval) {
                clearInterval(state.refreshInterval);
                state.refreshInterval = null;
            }
        };

        // 切换自动刷新
        const toggleAutoRefresh = () => {
            state.autoRefresh = !state.autoRefresh;
            if (state.autoRefresh) {
                startAutoRefresh();
                showMessage('已开启自动刷新', 'success');
            } else {
                stopAutoRefresh();
                showMessage('已关闭自动刷新', 'info');
            }
        };

        // 组件挂载时的初始化
        onMounted(async () => {
            console.log('应用已挂载');
            await checkServerStatus();
            
            if (state.isLoggedIn) {
                await loadUserInfo();
                await loadTop20Data();
                startAutoRefresh();
            }
        });

        return {
            state,
            loginForm,
            handleLogin,
            handleLogout,
            loadTop20Data,
            toggleAutoRefresh,
            checkServerStatus
        };
    },

    template: `
        <div>
            <!-- 登录界面 -->
            <div v-if="!state.isLoggedIn" class="login-container">
                <div class="login-card">
                    <div class="login-header">
                        <h1 style="margin: 0; font-size: 24px;">📊 股票TOP20监控</h1>
                        <p style="margin: 10px 0 0 0; opacity: 0.9;">实时监控策略股票排行榜</p>
                    </div>
                    <div class="login-body">
                        <form @submit.prevent="handleLogin">
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px;">用户名</label>
                                <input 
                                    v-model="loginForm.username" 
                                    type="text"
                                    placeholder="请输入用户名"
                                    style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;"
                                    required
                                />
                            </div>
                            <div style="margin-bottom: 16px;">
                                <label style="display: block; margin-bottom: 4px;">密码</label>
                                <input 
                                    v-model="loginForm.password" 
                                    type="password"
                                    placeholder="请输入密码"
                                    style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px;"
                                    required
                                />
                            </div>
                            <button 
                                type="submit"
                                :disabled="loginForm.loading"
                                style="width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;"
                            >
                                {{ loginForm.loading ? '登录中...' : '登录' }}
                            </button>
                        </form>
                        <div style="margin-top: 16px; padding: 12px; background: #f0f8ff; border-radius: 6px; font-size: 14px;">
                            <div><strong>测试账号：</strong></div>
                            <div>👨‍💼 管理员 - 用户名: admin, 密码: admin123</div>
                            <div>👤 普通用户 - 用户名: user, 密码: user123</div>
                        </div>
                        <div style="margin-top: 16px; text-align: center; color: #666; font-size: 12px;">
                            <div v-if="state.serverStatus === 'healthy'" style="color: #18a058;">
                                🟢 服务器连接正常
                            </div>
                            <div v-else-if="state.serverStatus === 'unhealthy'" style="color: #d03050;">
                                🔴 服务器连接失败，请检查后端服务
                            </div>
                            <div v-else style="color: #909399;">
                                🟡 检查服务器状态中...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主界面 -->
            <div v-else class="container">
                <div class="main-layout">
                    <!-- 头部 -->
                    <div class="layout-header">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <h2 style="margin: 0; font-size: 20px;">📊 股票TOP20监控系统</h2>
                                <p style="margin: 4px 0 0 0; opacity: 0.8; font-size: 14px;">实时策略股票排行榜</p>
                            </div>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <span style="color: rgba(255,255,255,0.8);">
                                    👋 {{ state.user?.username }}
                                </span>
                                <button 
                                    @click="loadTop20Data" 
                                    :disabled="state.loading"
                                    style="padding: 6px 12px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 4px; cursor: pointer;"
                                >
                                    {{ state.loading ? '🔄 刷新中...' : '🔄 刷新' }}
                                </button>
                                <button 
                                    @click="toggleAutoRefresh"
                                    style="padding: 6px 12px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 4px; cursor: pointer;"
                                >
                                    {{ state.autoRefresh ? '🔄 自动刷新' : '⏸️ 手动刷新' }}
                                </button>
                                <button 
                                    @click="handleLogout"
                                    style="padding: 6px 12px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 4px; cursor: pointer;"
                                >
                                    🚪 退出
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 内容区域 -->
                    <div class="layout-content">
                        <div v-if="state.loading" style="text-align: center; padding: 40px;">
                            <div style="font-size: 24px;">🔄</div>
                            <p>加载中...</p>
                        </div>
                        <div v-else-if="state.top20Data && state.top20Data.stocks.length > 0">
                            <!-- 统计信息 -->
                            <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 16px; margin-bottom: 20px;">
                                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 24px; font-weight: bold; color: #333;">{{ state.top20Data.total_count }}</div>
                                    <div style="color: #666; margin-top: 4px;">📊 总数量</div>
                                </div>
                                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 24px; font-weight: bold; color: #18a058;">{{ state.top20Data.positive_count }}</div>
                                    <div style="color: #666; margin-top: 4px;">📈 上涨数量</div>
                                </div>
                                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 24px; font-weight: bold; color: #333;">{{ state.top20Data.avg_change }}%</div>
                                    <div style="color: #666; margin-top: 4px;">📊 平均涨幅</div>
                                </div>
                                <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); text-align: center;">
                                    <div style="font-size: 24px; font-weight: bold; color: #f56565;">{{ state.top20Data.max_gain }}%</div>
                                    <div style="color: #666; margin-top: 4px;">🚀 最高涨幅</div>
                                </div>
                            </div>

                            <!-- 数据表格 -->
                            <div style="background: white; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); overflow: hidden;">
                                <div style="padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center;">
                                    <h3 style="margin: 0;">🏆 TOP20股票排行榜</h3>
                                    <span style="font-size: 14px; color: #909399;">
                                        ⏰ 更新时间: {{ state.top20Data.update_time }}
                                    </span>
                                </div>
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f8f9fa;">
                                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid #eee;">排名</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">股票代码</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">股票名称</th>
                                                <th style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">当前价格</th>
                                                <th style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">涨跌幅</th>
                                                <th style="padding: 12px; text-align: center; border-bottom: 1px solid #eee;">入选日期</th>
                                                <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">概念</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="stock in state.top20Data.stocks" :key="stock.stock_code" style="border-bottom: 1px solid #f0f0f0;">
                                                <td style="padding: 12px; text-align: center;">
                                                    <span :style="{
                                                        display: 'inline-flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        width: '24px',
                                                        height: '24px',
                                                        borderRadius: '50%',
                                                        color: 'white',
                                                        fontWeight: 'bold',
                                                        fontSize: '12px',
                                                        background: stock.rank === 1 ? '#ffd700' : stock.rank === 2 ? '#c0c0c0' : stock.rank === 3 ? '#cd7f32' : '#909399'
                                                    }">
                                                        {{ stock.rank }}
                                                    </span>
                                                </td>
                                                <td style="padding: 12px; font-weight: bold;">{{ stock.stock_code }}</td>
                                                <td style="padding: 12px; font-weight: 500;">{{ stock.stock_name }}</td>
                                                <td style="padding: 12px; text-align: right; font-family: monospace;">¥{{ stock.current_price }}</td>
                                                <td style="padding: 12px; text-align: right; font-family: monospace;" :style="{
                                                    color: stock.change_pct > 0 ? '#18a058' : stock.change_pct < 0 ? '#d03050' : '#909399',
                                                    fontWeight: 'bold'
                                                }">
                                                    {{ stock.change_pct > 0 ? '+' : '' }}{{ stock.change_pct }}%
                                                </td>
                                                <td style="padding: 12px; text-align: center; font-family: monospace;">{{ stock.entry_date }}</td>
                                                <td style="padding: 12px; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" :title="stock.concepts">
                                                    {{ stock.concepts || '-' }}
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div v-else style="text-align: center; padding: 60px 20px;">
                            <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                            <h3 style="color: #666; margin-bottom: 8px;">暂无数据</h3>
                            <p style="color: #999; margin-bottom: 20px;">
                                请等待任务调度器更新数据，或检查数据源是否正常
                            </p>
                            <button 
                                @click="loadTop20Data" 
                                :disabled="state.loading"
                                style="padding: 10px 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 6px; cursor: pointer;"
                            >
                                重新加载
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};

// 创建并挂载应用
console.log('开始创建Vue应用...');
createApp(App).mount('#app');
console.log('Vue应用创建完成');
