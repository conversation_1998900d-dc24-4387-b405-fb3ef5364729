// 简化版前端应用 - 用于测试
const { createApp, ref, reactive, onMounted } = Vue;

// 测试应用
const TestApp = {
    setup() {
        const message = ref('Hello World!');
        const count = ref(0);
        
        const increment = () => {
            count.value++;
        };
        
        onMounted(() => {
            console.log('Vue应用已挂载');
        });
        
        return {
            message,
            count,
            increment
        };
    },
    
    template: `
        <div style="padding: 20px; font-family: Arial, sans-serif;">
            <h1>🧪 Vue测试页面</h1>
            <p>{{ message }}</p>
            <p>计数器: {{ count }}</p>
            <button @click="increment" style="padding: 10px 20px; font-size: 16px;">
                点击增加
            </button>
            <hr style="margin: 20px 0;">
            <div>
                <h2>系统状态</h2>
                <p>✅ Vue.js 已加载</p>
                <p>✅ 前端服务器运行正常</p>
                <p>🔗 后端API: <a href="http://127.0.0.1:8000/docs" target="_blank">http://127.0.0.1:8000/docs</a></p>
            </div>
        </div>
    `
};

// 创建并挂载应用
console.log('开始创建Vue应用...');
createApp(TestApp).mount('#app');
console.log('Vue应用创建完成');
