// 股票TOP20监控前端应用
const { createApp, ref, reactive, onMounted, computed, h } = Vue;
const { 
    NConfigProvider, NMessageProvider, NDialogProvider, NNotificationProvider,
    NLayout, NLayoutHeader, NLayoutContent, NLayoutSider,
    NMenu, NCard, NButton, NForm, NFormItem, NInput, NSpace,
    NDataTable, NTag, NStatistic, NGrid, NGridItem, NSpin,
    NAlert, NIcon, NTime, darkTheme, useMessage, useDialog, useNotification,
    NAvatar, NBadge, NTooltip
} = naive;

// API配置
const API_BASE_URL = 'http://127.0.0.1:8000';

// API工具函数
const api = {
    baseURL: API_BASE_URL,
    token: localStorage.getItem('token'),
    
    setToken(token) {
        this.token = token;
        localStorage.setItem('token', token);
    },
    
    clearToken() {
        this.token = null;
        localStorage.removeItem('token');
    },
    
    async request(url, options = {}) {
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...(this.token && { 'Authorization': `Bearer ${this.token}` })
            },
            ...options
        };
        
        try {
            const response = await fetch(this.baseURL + url, config);
            
            if (response.status === 401) {
                this.clearToken();
                throw new Error('登录已过期，请重新登录');
            }
            
            if (!response.ok) {
                const error = await response.json().catch(() => ({ detail: '请求失败' }));
                throw new Error(error.detail || '请求失败');
            }
            
            return response.json();
        } catch (error) {
            if (error.name === 'TypeError' && error.message.includes('fetch')) {
                throw new Error('无法连接到服务器，请检查后端服务是否启动');
            }
            throw error;
        }
    },
    
    async login(username, password) {
        const response = await this.request('/api/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
        this.setToken(response.access_token);
        return response;
    },
    
    async getTop20() {
        return this.request('/api/top20');
    },
    
    async getUserInfo() {
        return this.request('/api/user/me');
    },
    
    async healthCheck() {
        return this.request('/api/health');
    }
};

// 内部组件 - 包含业务逻辑
const MainApp = {
    setup() {
        const message = useMessage();
        const dialog = useDialog();
        const notification = useNotification();
        
        const state = reactive({
            isLoggedIn: !!api.token,
            user: null,
            loading: false,
            top20Data: null,
            autoRefresh: true,
            refreshInterval: null,
            serverStatus: 'unknown'
        });
        
        const loginForm = reactive({
            username: '',
            password: '',
            loading: false
        });

        // 检查服务器状态
        const checkServerStatus = async () => {
            try {
                await api.healthCheck();
                state.serverStatus = 'healthy';
            } catch (error) {
                state.serverStatus = 'unhealthy';
                console.error('服务器健康检查失败:', error);
            }
        };

        // 登录
        const handleLogin = async () => {
            if (!loginForm.username || !loginForm.password) {
                message.error('请输入用户名和密码');
                return;
            }
            
            loginForm.loading = true;
            try {
                await api.login(loginForm.username, loginForm.password);
                state.isLoggedIn = true;
                message.success('登录成功');
                await loadUserInfo();
                await loadTop20Data();
                startAutoRefresh();
            } catch (error) {
                message.error(error.message);
            } finally {
                loginForm.loading = false;
            }
        };

        // 登出
        const handleLogout = () => {
            dialog.warning({
                title: '确认登出',
                content: '确定要退出登录吗？',
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => {
                    api.clearToken();
                    state.isLoggedIn = false;
                    state.user = null;
                    state.top20Data = null;
                    stopAutoRefresh();
                    message.success('已退出登录');
                }
            });
        };

        // 加载用户信息
        const loadUserInfo = async () => {
            try {
                state.user = await api.getUserInfo();
            } catch (error) {
                console.error('加载用户信息失败:', error);
                message.error('加载用户信息失败');
            }
        };

        // 加载TOP20数据
        const loadTop20Data = async () => {
            state.loading = true;
            try {
                state.top20Data = await api.getTop20();
                if (state.top20Data.stocks.length === 0) {
                    message.warning('暂无TOP20数据，请等待数据更新');
                }
            } catch (error) {
                message.error('加载数据失败: ' + error.message);
                console.error('加载TOP20数据失败:', error);
            } finally {
                state.loading = false;
            }
        };

        // 自动刷新
        const startAutoRefresh = () => {
            if (state.refreshInterval) return;
            state.refreshInterval = setInterval(() => {
                if (state.autoRefresh && state.isLoggedIn) {
                    loadTop20Data();
                }
            }, 60000); // 每分钟刷新一次
        };

        const stopAutoRefresh = () => {
            if (state.refreshInterval) {
                clearInterval(state.refreshInterval);
                state.refreshInterval = null;
            }
        };

        // 切换自动刷新
        const toggleAutoRefresh = () => {
            state.autoRefresh = !state.autoRefresh;
            if (state.autoRefresh) {
                startAutoRefresh();
                message.success('已开启自动刷新');
            } else {
                stopAutoRefresh();
                message.info('已关闭自动刷新');
            }
        };

        // 表格列定义
        const columns = [
            {
                title: '排名',
                key: 'rank',
                width: 80,
                render: (row) => {
                    const rank = row.rank;
                    let className = 'rank-other';
                    if (rank === 1) className = 'rank-1';
                    else if (rank === 2) className = 'rank-2';
                    else if (rank === 3) className = 'rank-3';
                    
                    return h('span', { class: `rank-badge ${className}` }, rank);
                }
            },
            {
                title: '股票代码',
                key: 'stock_code',
                width: 100,
                render: (row) => h('strong', row.stock_code)
            },
            {
                title: '股票名称',
                key: 'stock_name',
                width: 120,
                render: (row) => h('span', { style: 'font-weight: 500;' }, row.stock_name)
            },
            {
                title: '当前价格',
                key: 'current_price',
                width: 100,
                render: (row) => h('span', { style: 'font-family: monospace;' }, `¥${row.current_price}`)
            },
            {
                title: '涨跌幅',
                key: 'change_pct',
                width: 100,
                render: (row) => {
                    const pct = row.change_pct;
                    const className = pct > 0 ? 'positive' : pct < 0 ? 'negative' : 'neutral';
                    return h('span', { 
                        class: className,
                        style: 'font-family: monospace;'
                    }, `${pct > 0 ? '+' : ''}${pct}%`);
                }
            },
            {
                title: '入选日期',
                key: 'entry_date',
                width: 120,
                render: (row) => h('span', { style: 'font-family: monospace;' }, row.entry_date)
            },
            {
                title: '概念',
                key: 'concepts',
                ellipsis: {
                    tooltip: true
                },
                render: (row) => {
                    const concepts = row.concepts;
                    if (!concepts) return '-';
                    
                    const tags = concepts.split('、').slice(0, 3);
                    return h('div', tags.map(tag => 
                        h(NTag, { 
                            size: 'small', 
                            type: 'info',
                            style: 'margin-right: 4px; margin-bottom: 2px;'
                        }, tag)
                    ));
                }
            }
        ];

        // 组件挂载时的初始化
        onMounted(async () => {
            await checkServerStatus();
            
            if (state.isLoggedIn) {
                await loadUserInfo();
                await loadTop20Data();
                startAutoRefresh();
            }
        });

        return {
            state,
            loginForm,
            columns,
            handleLogin,
            handleLogout,
            loadTop20Data,
            toggleAutoRefresh,
            checkServerStatus
        };
    },

    template: `
                        <!-- 登录界面 -->
                        <div v-if="!state.isLoggedIn" class="login-container">
                            <div class="login-card">
                                <div class="login-header">
                                    <h1 style="margin: 0; font-size: 24px;">📊 股票TOP20监控</h1>
                                    <p style="margin: 10px 0 0 0; opacity: 0.9;">实时监控策略股票排行榜</p>
                                </div>
                                <div class="login-body">
                                    <n-form>
                                        <n-form-item label="用户名">
                                            <n-input 
                                                v-model:value="loginForm.username" 
                                                placeholder="请输入用户名"
                                                size="large"
                                                @keyup.enter="handleLogin"
                                            />
                                        </n-form-item>
                                        <n-form-item label="密码">
                                            <n-input 
                                                v-model:value="loginForm.password" 
                                                type="password" 
                                                placeholder="请输入密码"
                                                size="large"
                                                @keyup.enter="handleLogin"
                                            />
                                        </n-form-item>
                                        <n-form-item>
                                            <n-button 
                                                type="primary" 
                                                block 
                                                size="large"
                                                :loading="loginForm.loading"
                                                @click="handleLogin"
                                            >
                                                登录
                                            </n-button>
                                        </n-form-item>
                                    </n-form>
                                    <n-alert type="info" style="margin-top: 16px;">
                                        <div><strong>测试账号：</strong></div>
                                        <div>👨‍💼 管理员 - 用户名: admin, 密码: admin123</div>
                                        <div>👤 普通用户 - 用户名: user, 密码: user123</div>
                                    </n-alert>
                                    <div style="margin-top: 16px; text-align: center; color: #666; font-size: 12px;">
                                        <div v-if="state.serverStatus === 'healthy'" style="color: #18a058;">
                                            🟢 服务器连接正常
                                        </div>
                                        <div v-else-if="state.serverStatus === 'unhealthy'" style="color: #d03050;">
                                            🔴 服务器连接失败，请检查后端服务
                                        </div>
                                        <div v-else style="color: #909399;">
                                            🟡 检查服务器状态中...
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主界面 -->
                        <div v-else class="container">
                            <div class="main-layout">
                                <!-- 头部 -->
                                <div class="layout-header">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <h2 style="margin: 0; font-size: 20px;">📊 股票TOP20监控系统</h2>
                                            <p style="margin: 4px 0 0 0; opacity: 0.8; font-size: 14px;">实时策略股票排行榜</p>
                                        </div>
                                        <n-space>
                                            <span style="color: rgba(255,255,255,0.8);">
                                                👋 {{ state.user?.username }}
                                            </span>
                                            <n-button 
                                                @click="loadTop20Data" 
                                                :loading="state.loading"
                                                ghost
                                                size="small"
                                            >
                                                🔄 刷新
                                            </n-button>
                                            <n-button 
                                                @click="toggleAutoRefresh"
                                                :type="state.autoRefresh ? 'success' : 'default'"
                                                ghost
                                                size="small"
                                            >
                                                {{ state.autoRefresh ? '🔄 自动刷新' : '⏸️ 手动刷新' }}
                                            </n-button>
                                            <n-button @click="handleLogout" ghost size="small">
                                                🚪 退出
                                            </n-button>
                                        </n-space>
                                    </div>
                                </div>

                                <!-- 内容区域 -->
                                <div class="layout-content">
                                    <n-spin :show="state.loading">
                                        <div v-if="state.top20Data && state.top20Data.stocks.length > 0">
                                            <!-- 统计信息 -->
                                            <n-grid :cols="4" :x-gap="16" class="stats-card">
                                                <n-grid-item>
                                                    <n-card>
                                                        <n-statistic label="📊 总数量" :value="state.top20Data.total_count" />
                                                    </n-card>
                                                </n-grid-item>
                                                <n-grid-item>
                                                    <n-card>
                                                        <n-statistic label="📈 上涨数量" :value="state.top20Data.positive_count" />
                                                    </n-card>
                                                </n-grid-item>
                                                <n-grid-item>
                                                    <n-card>
                                                        <n-statistic 
                                                            label="📊 平均涨幅" 
                                                            :value="state.top20Data.avg_change" 
                                                            suffix="%" 
                                                        />
                                                    </n-card>
                                                </n-grid-item>
                                                <n-grid-item>
                                                    <n-card>
                                                        <n-statistic 
                                                            label="🚀 最高涨幅" 
                                                            :value="state.top20Data.max_gain" 
                                                            suffix="%" 
                                                        />
                                                    </n-card>
                                                </n-grid-item>
                                            </n-grid>

                                            <!-- 数据表格 -->
                                            <n-card title="🏆 TOP20股票排行榜">
                                                <template #header-extra>
                                                    <span style="font-size: 14px; color: #909399;">
                                                        ⏰ 更新时间: {{ state.top20Data.update_time }}
                                                    </span>
                                                </template>
                                                <n-data-table 
                                                    :columns="columns" 
                                                    :data="state.top20Data.stocks"
                                                    :pagination="false"
                                                    :bordered="false"
                                                    size="small"
                                                    :scroll-x="800"
                                                />
                                            </n-card>
                                        </div>
                                        <div v-else style="text-align: center; padding: 60px 20px;">
                                            <div style="font-size: 48px; margin-bottom: 16px;">📊</div>
                                            <h3 style="color: #666; margin-bottom: 8px;">暂无数据</h3>
                                            <p style="color: #999; margin-bottom: 20px;">
                                                请等待任务调度器更新数据，或检查数据源是否正常
                                            </p>
                                            <n-button type="primary" @click="loadTop20Data" :loading="state.loading">
                                                重新加载
                                            </n-button>
                                        </div>
                                    </n-spin>
                                </div>
                            </div>
                        </div>
    `
};

// 主应用组件 - 提供全局Provider
const App = {
    template: `
        <n-config-provider>
            <n-message-provider>
                <n-dialog-provider>
                    <n-notification-provider>
                        <main-app />
                    </n-notification-provider>
                </n-dialog-provider>
            </n-message-provider>
        </n-config-provider>
    `,
    components: {
        'main-app': MainApp
    }
};

// 创建并挂载应用
createApp(App).mount('#app');
