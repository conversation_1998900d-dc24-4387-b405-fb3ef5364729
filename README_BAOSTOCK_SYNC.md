# K线数据同步器 - Baostock版本

## 📋 项目简介

这是一个基于 baostock 数据源的股票K线数据同步工具，用于替代原来的 adata 数据源，避免IP被封的问题。

## 🎯 主要特性

- **稳定数据源**: 使用 baostock 免费数据源，无IP限制
- **断点续传**: 支持中断后继续同步，避免重复下载
- **进度管理**: 实时显示同步进度，支持失败重试
- **数据格式兼容**: 与原有数据库表结构完全兼容
- **智能股票选择**: 优先使用现有数据中的股票，否则使用默认热门股票列表

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的依赖：
```bash
# 激活虚拟环境
source venv/bin/activate

# 升级 baostock 到最新版本
pip install baostock --upgrade
```

### 2. 测试连接

运行测试脚本验证环境：
```bash
python3 test_baostock_sync.py
```

### 3. 查看股票列表

查看将要同步的股票列表：
```bash
python3 sync_kline_by_concepts_baostock.py --list-only
```

### 4. 开始同步

运行完整同步：
```bash
python3 sync_kline_by_concepts_baostock.py
```

## 📊 数据库配置

脚本使用 `config.py` 中的数据库配置：
```python
DATABASE_URL = 'mysql+pymysql://root:tanxi219.@localhost:3306/choice'
```

## 📁 数据表结构

### stock_kline_data 表
```sql
CREATE TABLE stock_kline_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    stock_code VARCHAR(10) NOT NULL,
    stock_name VARCHAR(50) NOT NULL,
    trade_date DATE NOT NULL,
    open_price FLOAT,
    high_price FLOAT,
    low_price FLOAT,
    close_price FLOAT,
    volume INT,
    amount FLOAT,
    turnover_rate FLOAT
);
```

### sync_progress 表
```sql
CREATE TABLE sync_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sync_date DATE NOT NULL,
    total_stocks INT NOT NULL,
    completed_stocks INT DEFAULT 0,
    failed_stocks INT DEFAULT 0,
    completed_codes TEXT DEFAULT '',
    failed_codes TEXT DEFAULT '',
    status VARCHAR(20) DEFAULT 'running',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔧 配置参数

可以在脚本中调整以下参数：

```python
# 初始化同步器时的参数
syncer = ConceptBasedKlineSyncBaostock(
    top_count=18,              # 保留参数（兼容性）
    blocked_concepts=None,     # 保留参数（兼容性）
    lookback_months=6          # 回看几个月的数据
)
```

## 📈 默认股票列表

脚本包含以下热门股票：

**银行股**: 平安银行(000001)、招商银行(600036)、浦发银行(600000)、兴业银行(601166)、万科A(000002)

**科技股**: 中兴通讯(000063)、海康威视(002415)、东方财富(300059)

**新能源**: 宁德时代(300750)、比亚迪(002594)、阳光电源(300274)、赣锋锂业(002460)、亿纬锂能(300014)

**医药股**: 恒瑞医药(600276)、爱尔眼科(300015)、长春高新(000661)

**消费股**: 贵州茅台(600519)、五粮液(000858)、伊利股份(600887)、双汇发展(000895)、洋河股份(002304)、山西汾酒(600809)

## 🛠️ 命令行选项

```bash
# 显示帮助信息
python3 sync_kline_by_concepts_baostock.py --help

# 只显示股票列表，不同步数据
python3 sync_kline_by_concepts_baostock.py --list-only

# 完整运行（默认）
python3 sync_kline_by_concepts_baostock.py
```

## 📝 日志文件

同步过程中的日志会保存到：
- `concept_kline_sync_baostock.log` - 主要日志文件

## 🔄 与原版本的区别

| 特性 | 原版本 (adata) | 新版本 (baostock) |
|------|----------------|-------------------|
| 数据源 | adata | baostock |
| IP限制 | 容易被封 | 无限制 |
| 股票选择 | 基于概念分析 | 默认热门股票 + 现有数据 |
| 数据格式 | 原生格式 | 转换为兼容格式 |
| 延迟设置 | 3-8秒 | 1-3秒 |

## 🚨 注意事项

1. **数据库连接**: 确保MySQL服务正在运行，数据库连接配置正确
2. **网络连接**: baostock需要网络连接，但比adata更稳定
3. **数据完整性**: 首次运行会清空当天的K线数据，重新同步
4. **断点续传**: 支持中断后继续，不会重复下载已完成的股票

## 🔍 故障排除

### 常见问题

1. **baostock登录失败**
   ```bash
   pip install baostock --upgrade
   ```

2. **数据库连接失败**
   - 检查 `config.py` 中的数据库配置
   - 确保MySQL服务正在运行

3. **没有股票数据**
   - 脚本会自动使用默认股票列表
   - 可以手动修改 `get_default_stock_list()` 方法添加更多股票

## 📞 技术支持

如有问题，请检查：
1. 运行测试脚本 `python3 test_baostock_sync.py`
2. 查看日志文件 `concept_kline_sync_baostock.log`
3. 确认数据库连接和表结构
