#!/usr/bin/env python3
"""
测试代理池功能
"""

import logging
from sync_kline_by_concepts_chgip import ProxyPool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_proxy_pool():
    """测试代理池功能"""
    print("🧪 开始测试代理池功能...")
    
    # 创建代理池
    proxy_pool = ProxyPool()
    
    # 获取代理列表
    print("🌐 正在获取免费代理...")
    proxies = proxy_pool.get_free_proxies()
    
    if proxies:
        print(f"✅ 成功获取到 {len(proxies)} 个代理")
        print("📋 前5个代理:")
        for i, proxy in enumerate(proxies[:5], 1):
            print(f"  {i}. {proxy}")
        
        # 测试代理切换
        print("\n🔄 测试代理切换功能...")
        current_proxy = proxy_pool.get_current_proxy()
        print(f"当前代理: {current_proxy}")
        
        proxy_pool.switch_next_proxy()
        next_proxy = proxy_pool.get_current_proxy()
        print(f"下一个代理: {next_proxy}")
        
        # 测试代理失败标记
        print("\n❌ 测试代理失败标记...")
        if current_proxy:
            proxy_pool.mark_proxy_failed(current_proxy)
            print(f"已标记代理 {current_proxy} 为失败")
            print(f"剩余代理数: {len(proxy_pool.proxies)}")
        
    else:
        print("❌ 未能获取到任何代理")
    
    print("\n🎉 代理池测试完成")

if __name__ == '__main__':
    test_proxy_pool()