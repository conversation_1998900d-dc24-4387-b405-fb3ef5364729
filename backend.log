2025-08-11 16:44:16,499 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/PycharmProjects/LongStock/venv/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     Started server process [89918]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
INFO:     127.0.0.1:51383 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51621 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:51621 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51621 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:57505 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:57505 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:59103 - "OPTIONS /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59103 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59103 - "OPTIONS /api/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59151 - "POST /api/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:59151 - "POST /api/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:59249 - "POST /api/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:59249 - "OPTIONS /api/user/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:59249 - "GET /api/user/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:59249 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:30,551 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59249 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:44,723 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59350 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:45,374 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59350 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:45,587 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59350 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:45,752 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59350 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:01:45,916 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59350 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:02:30,907 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:59764 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:03:30,916 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:60258 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:04:30,915 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:60650 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:05:30,928 - backend_api - INFO - 从缓存表获取到 5 个TOP20股票
INFO:     127.0.0.1:61055 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:06:30,926 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:61462 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:07:31,916 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:61906 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:08:32,918 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:62373 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:09:33,922 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:62873 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:10:34,920 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:63402 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:63859 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:11:35,921 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:63859 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:12:36,923 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:64286 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:13:37,920 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:64731 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:14:38,940 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:65204 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:15:39,916 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:49389 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:16:40,915 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:49884 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:17:41,912 - backend_api - INFO - 从缓存表获取到 0 个TOP20股票
INFO:     127.0.0.1:50322 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:18:30,908 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:50709 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:19:31,921 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:51307 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:51577 - "OPTIONS /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51577 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:51577 - "OPTIONS /api/user/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:51577 - "GET /api/user/me HTTP/1.1" 200 OK
2025-08-11 17:20:02,450 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:51577 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:21:02,870 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52009 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52251 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52251 - "GET /api/user/me HTTP/1.1" 200 OK
2025-08-11 17:21:34,079 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52251 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52478 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:22:02,893 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52478 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:22:34,870 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52706 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:52864 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52868 - "POST /api/login HTTP/1.1" 200 OK
2025-08-11 17:22:55,439 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52870 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:23:02,862 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:52931 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:23:34,865 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:53161 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:24:02,862 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:53360 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:24:34,858 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:53576 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:25:02,861 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:53757 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:25:34,856 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:53985 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:26:03,855 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:54170 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:26:34,854 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:54382 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:27:04,863 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:54580 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:27:35,858 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:54821 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:28:02,853 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:55019 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:28:36,852 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:55301 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:29:03,860 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:55477 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:29:37,847 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:55653 - "GET /api/top20 HTTP/1.1" 200 OK
2025-08-11 17:30:38,850 - backend_api - INFO - 从缓存表获取到 20 个TOP20股票
INFO:     127.0.0.1:55839 - "GET /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55989 - "GET /api/top20 HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:56141 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:56141 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:56290 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:56441 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:56590 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:56767 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:56913 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57071 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57215 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57352 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57506 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57657 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:57657 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57786 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:57938 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58116 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58295 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58448 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58639 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58802 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:58960 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59118 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59281 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59444 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:59444 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59593 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59739 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:59907 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     127.0.0.1:55170 - "OPTIONS /api/top20 HTTP/1.1" 200 OK
INFO:     127.0.0.1:55170 - "GET /api/top20 HTTP/1.1" 403 Forbidden
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [89918]
