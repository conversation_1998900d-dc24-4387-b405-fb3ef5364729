# 股票TOP20监控Web应用

基于FastAPI + Naive UI的股票TOP20实时监控系统，提供登录认证、数据展示和自动刷新功能。

## 🎯 功能特性

### 核心功能
- **用户认证**: 基于JWT的登录系统
- **实时监控**: TOP20股票排行榜实时展示
- **自动刷新**: 每分钟自动更新数据
- **响应式界面**: 基于Naive UI的现代化前端
- **数据缓存**: 数据库缓存机制，提高响应速度

### 技术栈
- **后端**: FastAPI + SQLAlchemy + MySQL
- **前端**: Vue 3 + Naive UI
- **认证**: JWT + bcrypt密码加密
- **数据库**: MySQL (复用现有数据库)

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保已激活虚拟环境
source venv/bin/activate

# 安装依赖（如果还没安装）
pip install fastapi uvicorn passlib[bcrypt] python-jose python-multipart
```

### 2. 数据库初始化
```bash
# 创建必要的数据库表
python3 init_web_database.py
```

### 3. 启动Web应用
```bash
# 方式1: 使用启动脚本
./start_web_app.sh

# 方式2: 直接启动
python3 -m uvicorn web_app:app --host 127.0.0.1 --port 8001
```

### 4. 访问应用
打开浏览器访问: http://127.0.0.1:8001

## 👤 测试账号

| 用户类型 | 用户名 | 密码 | 权限 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 完整访问 |
| 普通用户 | user | user123 | 查看数据 |

## 📊 数据流程

### 数据更新流程
1. **task_scheduler.py** 每分钟在交易时间内执行TOP20监控
2. **send_top20_to_dingtalk.py** 获取实时股票数据并发送到钉钉
3. **Web应用** 从数据库缓存表读取数据展示

### 数据表结构

#### top20_cache (TOP20缓存表)
```sql
CREATE TABLE top20_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rank_num INT NOT NULL COMMENT '排名',
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    change_pct DECIMAL(8,2) NOT NULL COMMENT '涨跌幅(%)',
    entry_date DATE NOT NULL COMMENT '入选日期',
    concepts TEXT COMMENT '概念',
    entry_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '入选价格',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 🔧 配置说明

### 环境变量
- `DATABASE_URL`: MySQL数据库连接字符串 (在config.py中配置)

### JWT配置
- `SECRET_KEY`: JWT密钥 (生产环境请修改)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: 令牌过期时间 (默认30分钟)

### 服务器配置
- 默认端口: 8001
- 默认主机: 127.0.0.1

## 📱 界面功能

### 登录界面
- 用户名/密码登录
- 错误提示
- 测试账号说明

### 主界面
- **统计卡片**: 总数量、上涨数量、平均涨幅、最高涨幅
- **数据表格**: 排名、股票代码、名称、价格、涨跌幅、入选日期、概念
- **自动刷新**: 每分钟自动更新数据
- **手动刷新**: 点击刷新按钮立即更新

## 🔄 与任务调度器集成

### task_scheduler.py 集成
Web应用与现有的任务调度器完美集成：

1. **每日任务**: 凌晨7:30执行数据处理流程
2. **TOP20监控**: 交易时间内每分钟执行
   - 上午: 9:30-11:30
   - 下午: 13:00-15:00

### 启动完整系统
```bash
# 启动任务调度器 (后台运行)
nohup python3 task_scheduler.py > scheduler.log 2>&1 &

# 启动Web应用
./start_web_app.sh
```

## 🛠️ 开发说明

### 项目结构
```
├── web_app.py              # Web应用主文件
├── init_web_database.py    # 数据库初始化脚本
├── start_web_app.sh        # Web应用启动脚本
├── task_scheduler.py       # 任务调度器 (已集成TOP20监控)
├── send_top20_to_dingtalk.py # TOP20数据获取和钉钉推送
└── config.py               # 配置文件
```

### API接口
- `POST /api/login`: 用户登录
- `GET /api/user/me`: 获取当前用户信息
- `GET /api/top20`: 获取TOP20股票数据

### 前端组件
- Vue 3 Composition API
- Naive UI 组件库
- 响应式设计
- 自动刷新机制

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查config.py中的DATABASE_URL配置
   - 确保MySQL服务正在运行

2. **缓存表为空**
   - 运行 `python3 init_web_database.py` 初始化数据
   - 确保task_scheduler.py正在运行

3. **登录失败**
   - 检查用户名密码是否正确
   - 查看控制台错误信息

4. **端口被占用**
   - 修改启动脚本中的端口号
   - 或者停止占用端口的进程

### 日志查看
```bash
# 查看Web应用日志
tail -f web_app.log

# 查看任务调度器日志
tail -f task_scheduler.log
```

## 🚀 部署建议

### 生产环境部署
1. 修改JWT密钥
2. 配置HTTPS
3. 使用Nginx反向代理
4. 配置系统服务自启动
5. 设置日志轮转

### 性能优化
1. 数据库索引优化
2. 缓存策略调整
3. 连接池配置
4. 静态资源CDN

## 📞 技术支持

如有问题，请检查：
1. 数据库连接是否正常
2. 虚拟环境是否激活
3. 依赖包是否完整安装
4. 端口是否被占用

---

**注意**: 这是一个演示系统，生产环境使用前请进行充分的安全性和性能测试。
