2025-08-04 21:14:58,010 - __main__ - INFO - ============================================================
2025-08-04 21:14:58,010 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-04 21:14:58,010 - __main__ - INFO - ============================================================
2025-08-04 21:14:58,026 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-04 21:14:58,027 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-04 21:14:58,027 - __main__ - INFO - 开始自动同步数据...
2025-08-04 21:14:58,126 - __main__ - INFO - 发现 1 个日期需要同步数据: ['2025-08-04']
2025-08-04 21:14:58,126 - __main__ - INFO - [1/1] 正在同步 2025-08-04 的数据...
2025-08-04 21:14:58,126 - __main__ - INFO - 开始获取 2025-08-04 的股票数据
2025-08-04 21:14:58,337 - __main__ - INFO - 成功获取到 0 条股票数据
2025-08-04 21:14:58,337 - __main__ - WARNING - 2025-08-04 没有股票数据
2025-08-04 21:14:58,337 - __main__ - ERROR - ❌ 2025-08-04 数据保存失败
2025-08-04 21:14:58,337 - __main__ - INFO - 自动同步完成，成功同步 0/1 天的数据
2025-08-04 21:14:58,337 - __main__ - WARNING - ⚠️ 部分数据同步失败，请检查日志
2025-08-04 21:14:58,337 - __main__ - INFO - 程序执行完成！
2025-08-05 02:04:46,343 - __main__ - INFO - ============================================================
2025-08-05 02:04:46,343 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 02:04:46,343 - __main__ - INFO - ============================================================
2025-08-05 02:04:47,911 - __main__ - INFO - 表 stock_daily_data 创建成功
2025-08-05 02:04:47,938 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 02:04:47,938 - __main__ - INFO - 开始自动同步数据...
2025-08-05 02:04:47,960 - __main__ - INFO - 所有日期的数据都已同步，无需下载
2025-08-05 02:04:47,960 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 02:04:47,960 - __main__ - INFO - 程序执行完成！
2025-08-05 02:06:55,549 - __main__ - INFO - ============================================================
2025-08-05 02:06:55,549 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 02:06:55,549 - __main__ - INFO - ============================================================
2025-08-05 02:06:55,651 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 02:06:55,661 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 02:06:55,661 - __main__ - INFO - 开始自动同步数据...
2025-08-05 02:06:55,675 - __main__ - INFO - 所有日期的数据都已同步，无需下载
2025-08-05 02:06:55,675 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 02:06:55,676 - __main__ - INFO - 程序执行完成！
2025-08-05 02:15:36,225 - __main__ - INFO - ============================================================
2025-08-05 02:15:36,225 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 02:15:36,225 - __main__ - INFO - ============================================================
2025-08-05 02:15:36,315 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 02:15:36,329 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 02:15:36,329 - __main__ - INFO - 开始自动同步数据...
2025-08-05 02:15:36,345 - __main__ - INFO - 所有日期的数据都已同步，无需下载
2025-08-05 02:15:36,345 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 02:15:36,345 - __main__ - INFO - 程序执行完成！
2025-08-05 02:25:00,741 - __main__ - INFO - ============================================================
2025-08-05 02:25:00,742 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 02:25:00,742 - __main__ - INFO - ============================================================
2025-08-05 02:25:00,835 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 02:25:00,847 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 02:25:00,847 - __main__ - INFO - 开始自动同步数据...
2025-08-05 02:25:03,237 - __main__ - INFO - 发现 150 个日期需要同步数据: ['2024-12-20', '2024-12-23', '2024-12-24', '2024-12-25', '2024-12-26', '2024-12-27', '2024-12-30', '2024-12-31', '2025-01-02', '2025-01-03', '2025-01-06', '2025-01-07', '2025-01-08', '2025-01-09', '2025-01-10', '2025-01-13', '2025-01-14', '2025-01-15', '2025-01-16', '2025-01-17', '2025-01-20', '2025-01-21', '2025-01-22', '2025-01-23', '2025-01-24', '2025-01-27', '2025-02-05', '2025-02-06', '2025-02-07', '2025-02-10', '2025-02-11', '2025-02-12', '2025-02-13', '2025-02-14', '2025-02-17', '2025-02-18', '2025-02-19', '2025-02-20', '2025-02-21', '2025-02-24', '2025-02-25', '2025-02-26', '2025-02-27', '2025-02-28', '2025-03-03', '2025-03-04', '2025-03-05', '2025-03-06', '2025-03-07', '2025-03-10', '2025-03-11', '2025-03-12', '2025-03-13', '2025-03-14', '2025-03-17', '2025-03-18', '2025-03-19', '2025-03-20', '2025-03-21', '2025-03-24', '2025-03-25', '2025-03-26', '2025-03-27', '2025-03-28', '2025-03-31', '2025-04-01', '2025-04-02', '2025-04-03', '2025-04-07', '2025-04-08', '2025-04-09', '2025-04-10', '2025-04-11', '2025-04-14', '2025-04-15', '2025-04-16', '2025-04-17', '2025-04-18', '2025-04-21', '2025-04-22', '2025-04-23', '2025-04-24', '2025-04-25', '2025-04-28', '2025-04-29', '2025-04-30', '2025-05-06', '2025-05-07', '2025-05-08', '2025-05-09', '2025-05-12', '2025-05-13', '2025-05-14', '2025-05-15', '2025-05-16', '2025-05-19', '2025-05-20', '2025-05-21', '2025-05-22', '2025-05-23', '2025-05-26', '2025-05-27', '2025-05-28', '2025-05-29', '2025-05-30', '2025-06-03', '2025-06-04', '2025-06-05', '2025-06-06', '2025-06-09', '2025-06-10', '2025-06-11', '2025-06-12', '2025-06-13', '2025-06-16', '2025-06-17', '2025-06-18', '2025-06-19', '2025-06-20', '2025-06-23', '2025-06-24', '2025-06-25', '2025-06-26', '2025-06-27', '2025-06-30', '2025-07-01', '2025-07-02', '2025-07-03', '2025-07-04', '2025-07-07', '2025-07-08', '2025-07-09', '2025-07-10', '2025-07-11', '2025-07-14', '2025-07-15', '2025-07-16', '2025-07-17', '2025-07-18', '2025-07-21', '2025-07-22', '2025-07-23', '2025-07-24', '2025-07-25', '2025-07-28', '2025-07-29', '2025-07-30', '2025-07-31', '2025-08-01', '2025-08-04']
2025-08-05 02:25:03,238 - __main__ - INFO - [1/150] 正在同步 2024-12-20 的数据...
2025-08-05 02:25:03,238 - __main__ - INFO - 开始获取 2024-12-20 的股票数据
2025-08-05 02:25:03,442 - __main__ - INFO - 成功获取到 22 条股票数据
2025-08-05 02:25:03,861 - __main__ - INFO - 成功保存 22 条数据到数据库，日期: 2024-12-20
2025-08-05 02:25:03,862 - __main__ - INFO - ✅ 2024-12-20 数据同步成功
2025-08-05 02:25:03,862 - __main__ - INFO - 等待 9.33 秒后继续...
2025-08-05 02:25:13,193 - __main__ - INFO - [2/150] 正在同步 2024-12-23 的数据...
2025-08-05 02:25:13,194 - __main__ - INFO - 开始获取 2024-12-23 的股票数据
2025-08-05 02:25:13,385 - __main__ - INFO - 成功获取到 11 条股票数据
2025-08-05 02:25:13,514 - __main__ - INFO - 成功保存 11 条数据到数据库，日期: 2024-12-23
2025-08-05 02:25:13,514 - __main__ - INFO - ✅ 2024-12-23 数据同步成功
2025-08-05 02:25:13,515 - __main__ - INFO - 等待 9.82 秒后继续...
2025-08-05 02:25:23,335 - __main__ - INFO - [3/150] 正在同步 2024-12-24 的数据...
2025-08-05 02:25:23,335 - __main__ - INFO - 开始获取 2024-12-24 的股票数据
2025-08-05 02:25:23,524 - __main__ - INFO - 成功获取到 24 条股票数据
2025-08-05 02:25:23,738 - __main__ - INFO - 成功保存 24 条数据到数据库，日期: 2024-12-24
2025-08-05 02:25:23,739 - __main__ - INFO - ✅ 2024-12-24 数据同步成功
2025-08-05 02:25:23,739 - __main__ - INFO - 等待 5.71 秒后继续...
2025-08-05 02:25:29,452 - __main__ - INFO - [4/150] 正在同步 2024-12-25 的数据...
2025-08-05 02:25:29,453 - __main__ - INFO - 开始获取 2024-12-25 的股票数据
2025-08-05 02:25:29,653 - __main__ - INFO - 成功获取到 11 条股票数据
2025-08-05 02:25:29,812 - __main__ - INFO - 成功保存 11 条数据到数据库，日期: 2024-12-25
2025-08-05 02:25:29,813 - __main__ - INFO - ✅ 2024-12-25 数据同步成功
2025-08-05 02:25:29,813 - __main__ - INFO - 等待 5.10 秒后继续...
2025-08-05 02:25:34,920 - __main__ - INFO - [5/150] 正在同步 2024-12-26 的数据...
2025-08-05 02:25:34,920 - __main__ - INFO - 开始获取 2024-12-26 的股票数据
2025-08-05 02:25:35,119 - __main__ - INFO - 成功获取到 19 条股票数据
2025-08-05 02:25:35,299 - __main__ - INFO - 成功保存 19 条数据到数据库，日期: 2024-12-26
2025-08-05 02:25:35,299 - __main__ - INFO - ✅ 2024-12-26 数据同步成功
2025-08-05 02:25:35,299 - __main__ - INFO - 等待 11.46 秒后继续...
2025-08-05 02:25:46,761 - __main__ - INFO - [6/150] 正在同步 2024-12-27 的数据...
2025-08-05 02:25:46,763 - __main__ - INFO - 开始获取 2024-12-27 的股票数据
2025-08-05 02:25:46,951 - __main__ - INFO - 成功获取到 28 条股票数据
2025-08-05 02:25:47,221 - __main__ - INFO - 成功保存 28 条数据到数据库，日期: 2024-12-27
2025-08-05 02:25:47,222 - __main__ - INFO - ✅ 2024-12-27 数据同步成功
2025-08-05 02:25:47,222 - __main__ - INFO - 等待 7.98 秒后继续...
2025-08-05 02:25:55,204 - __main__ - INFO - [7/150] 正在同步 2024-12-30 的数据...
2025-08-05 02:25:55,205 - __main__ - INFO - 开始获取 2024-12-30 的股票数据
2025-08-05 02:25:55,405 - __main__ - INFO - 成功获取到 8 条股票数据
2025-08-05 02:25:55,695 - __main__ - INFO - 成功保存 8 条数据到数据库，日期: 2024-12-30
2025-08-05 02:25:55,696 - __main__ - INFO - ✅ 2024-12-30 数据同步成功
2025-08-05 02:25:55,696 - __main__ - INFO - 等待 6.29 秒后继续...
2025-08-05 02:26:01,992 - __main__ - INFO - [8/150] 正在同步 2024-12-31 的数据...
2025-08-05 02:26:01,992 - __main__ - INFO - 开始获取 2024-12-31 的股票数据
2025-08-05 02:26:02,192 - __main__ - INFO - 成功获取到 16 条股票数据
2025-08-05 02:26:02,387 - __main__ - INFO - 成功保存 16 条数据到数据库，日期: 2024-12-31
2025-08-05 02:26:02,388 - __main__ - INFO - ✅ 2024-12-31 数据同步成功
2025-08-05 02:26:02,388 - __main__ - INFO - 等待 5.37 秒后继续...
2025-08-05 02:26:07,760 - __main__ - INFO - [9/150] 正在同步 2025-01-02 的数据...
2025-08-05 02:26:07,762 - __main__ - INFO - 开始获取 2025-01-02 的股票数据
2025-08-05 02:26:07,954 - __main__ - INFO - 成功获取到 32 条股票数据
2025-08-05 02:26:08,537 - __main__ - INFO - 成功保存 32 条数据到数据库，日期: 2025-01-02
2025-08-05 02:26:08,538 - __main__ - INFO - ✅ 2025-01-02 数据同步成功
2025-08-05 02:26:08,538 - __main__ - INFO - 等待 8.37 秒后继续...
2025-08-05 02:26:16,915 - __main__ - INFO - [10/150] 正在同步 2025-01-03 的数据...
2025-08-05 02:26:16,915 - __main__ - INFO - 开始获取 2025-01-03 的股票数据
2025-08-05 02:26:17,114 - __main__ - INFO - 成功获取到 16 条股票数据
2025-08-05 02:26:17,488 - __main__ - INFO - 成功保存 16 条数据到数据库，日期: 2025-01-03
2025-08-05 02:26:17,488 - __main__ - INFO - ✅ 2025-01-03 数据同步成功
2025-08-05 02:26:17,488 - __main__ - INFO - 等待 7.51 秒后继续...
2025-08-05 07:16:27,466 - __main__ - INFO - ============================================================
2025-08-05 07:16:27,466 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 07:16:27,466 - __main__ - INFO - ============================================================
2025-08-05 07:16:27,570 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 07:16:27,589 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 07:16:27,589 - __main__ - INFO - 开始自动同步数据...
2025-08-05 07:16:30,308 - __main__ - INFO - 所有日期的数据都已同步，无需下载
2025-08-05 07:16:30,309 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 07:16:30,309 - __main__ - INFO - 程序执行完成！
2025-08-05 23:51:01,697 - __main__ - INFO - ============================================================
2025-08-05 23:51:01,697 - __main__ - INFO - 股票数据自动同步程序启动
2025-08-05 23:51:01,697 - __main__ - INFO - ============================================================
2025-08-05 23:51:01,752 - __main__ - INFO - 表 stock_daily_data 已存在且结构正确
2025-08-05 23:51:01,757 - __main__ - INFO - 表 stock_market_data 已存在
2025-08-05 23:51:01,757 - __main__ - INFO - 开始自动同步数据...
2025-08-05 23:51:01,831 - __main__ - INFO - 发现 1 个日期需要同步数据: ['2025-08-04']
2025-08-05 23:51:01,831 - __main__ - INFO - [1/1] 正在同步 2025-08-04 的数据...
2025-08-05 23:51:01,831 - __main__ - INFO - 开始获取 2025-08-04 的股票数据
2025-08-05 23:51:02,010 - __main__ - INFO - 成功获取到 11 条股票数据
2025-08-05 23:51:02,042 - __main__ - INFO - 成功保存 11 条数据到数据库，日期: 2025-08-04
2025-08-05 23:51:02,042 - __main__ - INFO - ✅ 2025-08-04 数据同步成功
2025-08-05 23:51:02,042 - __main__ - INFO - 自动同步完成，成功同步 1/1 天的数据
2025-08-05 23:51:02,042 - __main__ - INFO - 🎉 所有数据同步完成！
2025-08-05 23:51:02,042 - __main__ - INFO - 程序执行完成！
