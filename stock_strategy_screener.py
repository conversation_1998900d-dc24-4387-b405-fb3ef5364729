#!/usr/bin/env python3
"""
股票策略筛选器
策略思路：
1. 即将要突破前高或者已经突破前高(即将突破前高在相差10个点以内,或者突破前高也需要再突破前高10个点以内后回撤)
2. 然后大幅回撤到最低点(幅度大于25%)
3. 从最低点上来有5.8%以上但低于10%的幅度
4. 打印出符合这个策略的股票，标注符合策略时的日期股价等内容
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, Column, Integer, String, Float, Date, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy_screener.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
engine = create_engine(Config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 策略结果表模型
class StrategyResult(Base):
    __tablename__ = 'strategy_results'

    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(20), nullable=False)
    entry_date = Column(Date, nullable=False)  # 入选日期
    entry_price = Column(Float, nullable=False)  # 入选价格
    previous_high_date = Column(Date, nullable=False)  # 前高日期
    previous_high_price = Column(Float, nullable=False)  # 前高价格
    breakthrough_type = Column(String(20), nullable=False)  # 突破类型
    breakthrough_date = Column(Date, nullable=True)  # 突破日期
    breakthrough_price = Column(Float, nullable=True)  # 突破价格
    high_point_date = Column(Date, nullable=False)  # 最高点日期
    high_point_price = Column(Float, nullable=False)  # 最高点价格
    low_point_date = Column(Date, nullable=False)  # 最低点日期
    low_point_price = Column(Float, nullable=False)  # 最低点价格
    pullback_ratio = Column(Float, nullable=False)  # 回撤幅度
    rebound_ratio = Column(Float, nullable=False)  # 反弹幅度
    current_ma5 = Column(Float, nullable=True)  # MA5
    current_ma10 = Column(Float, nullable=True)  # MA10
    volume = Column(Float, nullable=True)  # 成交量
    turnover_rate = Column(Float, nullable=True)  # 换手率
    created_at = Column(DateTime, default=datetime.now)  # 创建时间

    def __repr__(self):
        return f"<StrategyResult(stock_code='{self.stock_code}', entry_date='{self.entry_date}')>"

class StockStrategyScreener:
    """股票策略筛选器"""
    
    def __init__(self):
        self.session = SessionLocal()
        
    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()

    def create_strategy_table(self):
        """创建策略结果表"""
        try:
            Base.metadata.create_all(engine)
            logger.info("策略结果表创建成功")
        except Exception as e:
            logger.error(f"创建策略结果表失败: {str(e)}")

    def save_strategy_result(self, result_data):
        """保存策略结果到数据库"""
        try:
            # 检查是否已存在相同的记录（相同股票代码和入选日期）
            existing = self.session.query(StrategyResult).filter(
                StrategyResult.stock_code == result_data['stock_code'],
                StrategyResult.entry_date == result_data['entry_date']
            ).first()

            if existing:
                logger.info(f"股票 {result_data['stock_code']} 在 {result_data['entry_date']} 的策略结果已存在，跳过保存")
                return False

            # 创建新记录
            strategy_result = StrategyResult(
                stock_code=result_data['stock_code'],
                entry_date=datetime.strptime(result_data['entry_date'], '%Y-%m-%d').date(),
                entry_price=result_data['entry_price'],
                previous_high_date=datetime.strptime(result_data['previous_high_date'], '%Y-%m-%d').date(),
                previous_high_price=result_data['previous_high_price'],
                breakthrough_type=result_data['breakthrough_type'],
                breakthrough_date=datetime.strptime(result_data['breakthrough_date'], '%Y-%m-%d').date() if result_data['breakthrough_date'] else None,
                breakthrough_price=result_data['breakthrough_price'] if result_data['breakthrough_price'] else None,
                high_point_date=datetime.strptime(result_data['high_point_date'], '%Y-%m-%d').date(),
                high_point_price=result_data['high_point_price'],
                low_point_date=datetime.strptime(result_data['low_point_date'], '%Y-%m-%d').date(),
                low_point_price=result_data['low_point_price'],
                pullback_ratio=result_data['pullback_ratio'],
                rebound_ratio=result_data['rebound_ratio'],
                current_ma5=result_data['current_ma5'],
                current_ma10=result_data['current_ma10'],
                volume=result_data['volume'],
                turnover_rate=result_data['turnover_rate']
            )

            self.session.add(strategy_result)
            self.session.commit()
            logger.info(f"✅ 股票 {result_data['stock_code']} 策略结果保存成功")
            return True

        except Exception as e:
            self.session.rollback()
            logger.error(f"保存策略结果失败: {str(e)}")
            return False

    def get_saved_strategy_results(self, limit=None):
        """从数据库获取已保存的策略结果"""
        try:
            query = self.session.query(StrategyResult).order_by(StrategyResult.entry_date.desc())
            if limit:
                query = query.limit(limit)

            results = query.all()

            # 转换为字典格式，便于显示
            result_list = []
            for result in results:
                result_dict = {
                    'stock_code': result.stock_code,
                    'entry_date': result.entry_date.strftime('%Y-%m-%d'),
                    'entry_price': result.entry_price,
                    'previous_high_date': result.previous_high_date.strftime('%Y-%m-%d'),
                    'previous_high_price': result.previous_high_price,
                    'breakthrough_type': result.breakthrough_type,
                    'breakthrough_date': result.breakthrough_date.strftime('%Y-%m-%d') if result.breakthrough_date else '',
                    'breakthrough_price': result.breakthrough_price if result.breakthrough_price else 0,
                    'high_point_date': result.high_point_date.strftime('%Y-%m-%d'),
                    'high_point_price': result.high_point_price,
                    'low_point_date': result.low_point_date.strftime('%Y-%m-%d'),
                    'low_point_price': result.low_point_price,
                    'pullback_ratio': result.pullback_ratio,
                    'rebound_ratio': result.rebound_ratio,
                    'current_ma5': result.current_ma5,
                    'current_ma10': result.current_ma10,
                    'volume': result.volume,
                    'turnover_rate': result.turnover_rate,
                    'created_at': result.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
                result_list.append(result_dict)

            return result_list

        except Exception as e:
            logger.error(f"获取策略结果失败: {str(e)}")
            return []
    
    def get_stock_data(self, stock_code, days=120):
        """获取指定股票的K线数据"""
        try:
            # 计算起始日期
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            query = text("""
                SELECT stock_code, trade_date, open_price, high_price, low_price, close_price,
                       volume, amount, turnover_rate, ma5, ma10, ma20,
                       high_3m, low_3m, limit_up_count_3m, limit_up_count_1m
                FROM stock_kline_data 
                WHERE stock_code = :stock_code 
                AND trade_date >= :start_date
                ORDER BY trade_date ASC
            """)
            
            result = self.session.execute(query, {
                'stock_code': stock_code,
                'start_date': start_date
            })
            
            df = pd.DataFrame(result.fetchall(), columns=[
                'stock_code', 'trade_date', 'open_price', 'high_price', 'low_price', 'close_price',
                'volume', 'amount', 'turnover_rate', 'ma5', 'ma10', 'ma20',
                'high_3m', 'low_3m', 'limit_up_count_3m', 'limit_up_count_1m'
            ])
            
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date').reset_index(drop=True)
                
                # 计算涨跌停标识
                df['pct_change'] = df['close_price'].pct_change() * 100
                df['is_limit_up'] = df['pct_change'] >= 9.8  # 涨停标识
                
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 数据失败: {str(e)}")
            return pd.DataFrame()
    
    def check_strategy_conditions(self, df, stock_code):
        """检查策略条件 - 只返回第一次符合条件的结果"""
        if df.empty or len(df) < 30:  # 至少需要30天数据
            return None

        # 遍历每一天，检查是否符合策略
        for i in range(30, len(df)):  # 从第30天开始检查，确保有足够的历史数据
            current_date = df.iloc[i]['trade_date']
            current_price = df.iloc[i]['close_price']

            # 获取当前日期前的数据
            history_df = df.iloc[:i+1].copy()

            # 条件1：检查是否即将突破前高或已经突破前高
            condition1_result = self._check_breakthrough_previous_high(history_df, i)
            if not condition1_result:
                continue

            # 条件2：检查是否有大幅回撤（>25%）
            condition2_result = self._check_major_pullback(history_df, i, condition1_result['high_point_idx'])
            if not condition2_result:
                continue

            # 条件3：检查是否从最低点反弹5.8%-10%
            condition3_result = self._check_rebound_range(history_df, i, condition2_result['low_point_idx'])
            if not condition3_result:
                continue

            # 如果所有条件都满足，返回第一次符合条件的结果
            first_result = {
                'stock_code': stock_code,
                'entry_date': current_date.strftime('%Y-%m-%d'),  # 入选日期
                'entry_price': current_price,  # 入选价格
                'previous_high_date': history_df.iloc[condition1_result['previous_high_idx']]['trade_date'].strftime('%Y-%m-%d'),
                'previous_high_price': history_df.iloc[condition1_result['previous_high_idx']]['close_price'],
                'breakthrough_type': condition1_result['breakthrough_type'],  # 突破类型：即将突破/已突破
                'breakthrough_date': condition1_result.get('breakthrough_date', ''),  # 突破日期（如果已突破）
                'breakthrough_price': condition1_result.get('breakthrough_price', 0),  # 突破价格（如果已突破）
                'high_point_date': history_df.iloc[condition1_result['high_point_idx']]['trade_date'].strftime('%Y-%m-%d'),
                'high_point_price': history_df.iloc[condition1_result['high_point_idx']]['close_price'],  # 最高价格
                'low_point_date': history_df.iloc[condition2_result['low_point_idx']]['trade_date'].strftime('%Y-%m-%d'),
                'low_point_price': history_df.iloc[condition2_result['low_point_idx']]['close_price'],  # 最低价格
                'pullback_ratio': condition2_result['pullback_ratio'],  # 回撤幅度
                'rebound_ratio': condition3_result['rebound_ratio'],
                'current_ma5': df.iloc[i]['ma5'],
                'current_ma10': df.iloc[i]['ma10'],
                'volume': df.iloc[i]['volume'],
                'turnover_rate': df.iloc[i]['turnover_rate']
            }

            # 找到第一次符合条件的记录就立即返回
            return first_result

        return None
    
    def _check_breakthrough_previous_high(self, df, current_idx, lookback_days=90):
        """检查是否即将突破前高或已经突破前高"""
        if current_idx < 30:  # 至少需要30天数据
            return None

        # 查找前高：在当前日期之前的历史最高点（至少30天前的数据）
        # 避免使用太近期的高点作为前高
        historical_end_idx = max(0, current_idx - 30)  # 至少30天前
        historical_start_idx = max(0, current_idx - lookback_days)  # 最多90天前

        if historical_end_idx <= historical_start_idx:
            return None

        historical_df = df.iloc[historical_start_idx:historical_end_idx]

        if historical_df.empty:
            return None

        # 找到历史最高点作为前高
        previous_high_price = historical_df['high_price'].max()
        previous_high_idx = historical_df['high_price'].idxmax()

        # 获取当前价格
        current_price = df.iloc[current_idx]['close_price']

        # 计算与前高的差距（以点数计算，假设股价在10-100元之间，10个点约为10%）
        price_diff_pct = ((current_price - previous_high_price) / previous_high_price) * 100

        breakthrough_type = None
        breakthrough_date = None
        breakthrough_price = None
        high_point_idx = current_idx  # 默认当前位置为高点

        # 情况1：即将突破前高（在10个点以内，即10%以内）
        if -10 <= price_diff_pct <= 0:
            breakthrough_type = "即将突破"
            high_point_idx = current_idx

        # 情况2：已经突破前高，但需要在突破后10个点以内回撤
        elif price_diff_pct > 0 and price_diff_pct <= 10:
            # 查找突破点：第一次超过前高的日期
            recent_df = df.iloc[historical_end_idx:current_idx+1]
            breakthrough_days = recent_df[recent_df['high_price'] > previous_high_price]

            if not breakthrough_days.empty:
                breakthrough_idx = breakthrough_days.index[0]
                breakthrough_date = df.iloc[breakthrough_idx]['trade_date'].strftime('%Y-%m-%d')
                breakthrough_price = df.iloc[breakthrough_idx]['high_price']
                breakthrough_type = "已突破"

                # 找到突破后的最高点
                after_breakthrough_df = df.iloc[breakthrough_idx:current_idx+1]
                max_price_after_breakthrough = after_breakthrough_df['high_price'].max()
                high_point_idx = after_breakthrough_df['high_price'].idxmax()
            else:
                return None
        else:
            return None

        if breakthrough_type:
            return {
                'previous_high_idx': previous_high_idx,
                'previous_high_price': previous_high_price,
                'breakthrough_type': breakthrough_type,
                'breakthrough_date': breakthrough_date,
                'breakthrough_price': breakthrough_price,
                'high_point_idx': high_point_idx,
                'price_diff_pct': price_diff_pct
            }

        return None
    
    def _check_major_pullback(self, df, current_idx, high_point_idx, min_pullback=25):
        """检查大幅回撤（>25%）"""
        if high_point_idx >= current_idx:
            return None
        
        # 从高点后开始查找最低点
        after_high_df = df.iloc[high_point_idx:current_idx+1]
        high_price = df.iloc[high_point_idx]['high_price']
        
        # 找到最低点
        min_price = after_high_df['low_price'].min()
        min_price_idx = after_high_df['low_price'].idxmin()
        
        # 计算回撤幅度
        pullback_ratio = ((high_price - min_price) / high_price) * 100
        
        if pullback_ratio >= min_pullback:
            return {
                'low_point_idx': min_price_idx,
                'low_point_price': min_price,
                'pullback_ratio': pullback_ratio
            }
        
        return None
    
    def _check_rebound_range(self, df, current_idx, low_point_idx, min_rebound=10, max_rebound=15):
        """检查反弹幅度（5.8%-10%）"""
        if low_point_idx >= current_idx:
            return None
        
        low_price = df.iloc[low_point_idx]['low_price']
        current_price = df.iloc[current_idx]['close_price']
        
        # 计算反弹幅度
        rebound_ratio = ((current_price - low_price) / low_price) * 100
        
        if min_rebound <= rebound_ratio <= max_rebound:
            return {
                'rebound_ratio': rebound_ratio
            }
        
        return None
    
    def get_all_stock_codes(self):
        """获取所有有K线数据的股票代码"""
        try:
            query = text("""
                SELECT DISTINCT stock_code 
                FROM stock_kline_data 
                WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL 4 MONTH)
                ORDER BY stock_code
            """)
            
            result = self.session.execute(query)
            stock_codes = [row[0] for row in result.fetchall()]
            
            logger.info(f"获取到 {len(stock_codes)} 个股票代码")
            return stock_codes
            
        except Exception as e:
            logger.error(f"获取股票代码失败: {str(e)}")
            return []
    
    def screen_stocks(self, stock_codes=None, max_stocks=None):
        """筛选符合策略的股票"""
        if stock_codes is None:
            stock_codes = self.get_all_stock_codes()

        if max_stocks:
            stock_codes = stock_codes[:max_stocks]

        logger.info(f"开始筛选 {len(stock_codes)} 个股票...")

        all_results = []
        processed_count = 0

        for i, stock_code in enumerate(stock_codes):
            try:
                logger.info(f"[{i+1}/{len(stock_codes)}] 分析股票: {stock_code}")

                # 获取股票数据
                df = self.get_stock_data(stock_code)

                if df.empty:
                    logger.warning(f"股票 {stock_code} 无数据，跳过")
                    continue

                # 检查策略条件（现在只返回第一次符合条件的结果）
                result = self.check_strategy_conditions(df, stock_code)

                if result:
                    all_results.append(result)
                    # 保存到数据库
                    self.save_strategy_result(result)
                    logger.info(f"✅ 股票 {stock_code} 符合策略")

                processed_count += 1

                # 每处理50个股票显示一次进度
                if processed_count % 50 == 0:
                    logger.info(f"📊 已处理 {processed_count} 个股票，找到 {len(all_results)} 个符合策略的股票")

            except Exception as e:
                logger.error(f"分析股票 {stock_code} 时发生错误: {str(e)}")
                continue

        logger.info(f"筛选完成！共处理 {processed_count} 个股票，找到 {len(all_results)} 个符合策略的股票")
        return all_results
    
    def print_results(self, results):
        """打印筛选结果"""
        if not results:
            print("❌ 未找到符合策略的股票")
            return

        print("=" * 120)
        print(f"🎯 策略筛选结果 - 共找到 {len(results)} 个符合策略的股票")
        print("=" * 120)

        # 按入选日期排序
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('entry_date', ascending=False)

        for i, result in results_df.iterrows():
            print(f"\n📈 股票 {i+1}:")
            print(f"   股票代码: {result['stock_code']}")
            print(f"   入选日期: {result['entry_date']}")
            print(f"   入选价格: {result['entry_price']:.2f}")
            print(f"   ")
            print(f"   📊 策略关键数据:")
            print(f"   前高价格: {result['previous_high_price']:.2f} (日期: {result['previous_high_date']})")
            print(f"   突破状态: {result['breakthrough_type']}")

            # 如果已突破，显示突破信息
            if result['breakthrough_type'] == "已突破" and result['breakthrough_date']:
                print(f"   突破日期: {result['breakthrough_date']} (价格: {result['breakthrough_price']:.2f})")

            print(f"   最高价格: {result['high_point_price']:.2f} (日期: {result['high_point_date']})")
            print(f"   最低价格: {result['low_point_price']:.2f} (日期: {result['low_point_date']})")
            print(f"   ")
            print(f"   📉 回撤幅度: {result['pullback_ratio']:.1f}%")
            print(f"   📈 反弹幅度: {result['rebound_ratio']:.1f}%")
            print(f"   ")
            print(f"   📋 技术指标:")
            ma5_str = f"{result['current_ma5']:.2f}" if result['current_ma5'] and not pd.isna(result['current_ma5']) else 'N/A'
            ma10_str = f"{result['current_ma10']:.2f}" if result['current_ma10'] and not pd.isna(result['current_ma10']) else 'N/A'
            volume_str = f"{result['volume']:,.0f}" if result['volume'] and not pd.isna(result['volume']) else '0'
            turnover_str = f"{result['turnover_rate']:.2f}%" if result['turnover_rate'] and not pd.isna(result['turnover_rate']) else 'N/A'

            print(f"   MA5: {ma5_str}")
            print(f"   MA10: {ma10_str}")
            print(f"   成交量: {volume_str}")
            print(f"   换手率: {turnover_str}")
            print("-" * 100)

    def generate_summary_report(self, results):
        """生成策略筛选统计报告"""
        if not results:
            return

        print("\n" + "=" * 100)
        print("📊 策略筛选统计报告")
        print("=" * 100)

        results_df = pd.DataFrame(results)

        # 基本统计
        total_stocks = len(results)
        print(f"📈 符合策略的股票总数: {total_stocks}")

        # 回撤幅度统计
        pullback_stats = results_df['pullback_ratio'].describe()
        print(f"\n📉 回撤幅度统计:")
        print(f"   平均回撤: {pullback_stats['mean']:.1f}%")
        print(f"   最大回撤: {pullback_stats['max']:.1f}%")
        print(f"   最小回撤: {pullback_stats['min']:.1f}%")

        # 反弹幅度统计
        rebound_stats = results_df['rebound_ratio'].describe()
        print(f"\n📈 反弹幅度统计:")
        print(f"   平均反弹: {rebound_stats['mean']:.1f}%")
        print(f"   最大反弹: {rebound_stats['max']:.1f}%")
        print(f"   最小反弹: {rebound_stats['min']:.1f}%")

        # 时间分布
        results_df['entry_month'] = pd.to_datetime(results_df['entry_date']).dt.strftime('%Y-%m')
        month_counts = results_df['entry_month'].value_counts().sort_index()
        print(f"\n📅 入选时间分布:")
        for month, count in month_counts.items():
            print(f"   {month}: {count} 个股票")

        # 最新入选股票
        results_df['entry_date_dt'] = pd.to_datetime(results_df['entry_date'])
        latest_stocks = results_df.nlargest(5, 'entry_date_dt')
        print(f"\n🕐 最新入选的5个股票:")
        for _, stock in latest_stocks.iterrows():
            print(f"   {stock['stock_code']} | {stock['entry_date']} | 价格: {stock['entry_price']:.2f} | 反弹: {stock['rebound_ratio']:.1f}%")

        print("=" * 100)

def main():
    """主函数"""
    print("🚀 股票策略筛选器启动")
    print("策略说明：")
    print("1. 即将要突破前高或者已经突破前高(即将突破前高在相差10个点以内,或者突破前高也需要再突破前高10个点以内后回撤)")
    print("2. 然后大幅回撤到最低点(幅度大于25%)")
    print("3. 从最低点上来有5.8%以上但低于10%的幅度")
    print("4. 每个股票只保留第一次符合策略的数据")
    print("5. 符合策略的股票将自动保存到数据库 strategy_results 表中")

    try:
        screener = StockStrategyScreener()

        # 创建策略结果表
        print("📊 初始化数据库表...")
        screener.create_strategy_table()

        # 默认筛选所有股票
        print("\n🚀 开始筛选所有股票...")
        print("💡 这可能需要一些时间，请耐心等待...")
        print("💾 符合策略的股票将自动保存到数据库中...")

        results = screener.screen_stocks()

        # 打印结果
        screener.print_results(results)

        # 生成统计报告
        if results:
            screener.generate_summary_report(results)

        # 显示数据库中的所有策略结果
        print("\n" + "=" * 120)
        print("📊 数据库中已保存的策略结果")
        print("=" * 120)

        saved_results = screener.get_saved_strategy_results()
        if saved_results:
            print(f"💾 数据库中共有 {len(saved_results)} 个策略结果")
            screener.print_results(saved_results)
        else:
            print("💾 数据库中暂无策略结果")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == '__main__':
    main()
