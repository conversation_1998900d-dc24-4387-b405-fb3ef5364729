#!/usr/bin/env python3
"""
小规模测试同步 - 只同步3个股票验证流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_kline_by_concepts_baostock import ConceptBasedKlineSyncBaostock
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_small_sync():
    """测试小规模同步"""
    print("🧪 开始小规模同步测试...")
    
    # 创建同步器
    syncer = ConceptBasedKlineSyncBaostock(lookback_months=1)  # 只回看1个月
    
    # 定义测试股票列表（只选3个）
    test_stocks = [
        "000001",  # 平安银行
        "600036",  # 招商银行
        "000002",  # 万科A
    ]
    
    print(f"📊 测试股票列表: {test_stocks}")
    print("⏰ 开始同步...")
    
    try:
        # 直接调用同步方法
        syncer.sync_kline_data(test_stocks)
        print("✅ 小规模同步测试完成")
        return True
    except Exception as e:
        print(f"❌ 同步测试失败: {str(e)}")
        return False
    finally:
        # 清理资源
        if hasattr(syncer, 'session'):
            syncer.session.close()

def check_sync_results():
    """检查同步结果"""
    print("\n🔍 检查同步结果...")
    
    try:
        from sqlalchemy import create_engine, text
        from config import Config
        
        engine = create_engine(Config.DATABASE_URL)
        with engine.connect() as conn:
            # 检查数据量
            result = conn.execute(text("SELECT COUNT(*) FROM stock_kline_data"))
            total_count = result.fetchone()[0]
            print(f"📊 总数据量: {total_count} 条")
            
            # 检查股票数量
            result = conn.execute(text("SELECT COUNT(DISTINCT stock_code) FROM stock_kline_data"))
            stock_count = result.fetchone()[0]
            print(f"📊 股票数量: {stock_count} 个")
            
            # 显示部分数据
            result = conn.execute(text("""
                SELECT stock_code, COUNT(*) as count, MIN(trade_date) as start_date, MAX(trade_date) as end_date
                FROM stock_kline_data 
                GROUP BY stock_code 
                ORDER BY stock_code
                LIMIT 5
            """))
            
            print("\n📋 数据详情:")
            for row in result.fetchall():
                print(f"  {row[0]}: {row[1]} 条数据 ({row[2]} ~ {row[3]})")
                
        return True
    except Exception as e:
        print(f"❌ 检查结果失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 K线数据同步小规模测试")
    print("=" * 50)
    
    # 执行同步测试
    sync_success = test_small_sync()
    
    if sync_success:
        # 检查结果
        check_success = check_sync_results()
        
        print("\n" + "=" * 50)
        print("🎯 测试结果:")
        print(f"  数据同步: {'✅ 成功' if sync_success else '❌ 失败'}")
        print(f"  结果检查: {'✅ 成功' if check_success else '❌ 失败'}")
        
        if sync_success and check_success:
            print("\n🎉 小规模测试完全成功！")
            print("💡 现在可以运行完整同步:")
            print("  python3 sync_kline_by_concepts_baostock.py")
        else:
            print("\n⚠️ 测试部分失败，请检查配置")
    else:
        print("\n❌ 同步测试失败，请检查配置")

if __name__ == '__main__':
    main()
