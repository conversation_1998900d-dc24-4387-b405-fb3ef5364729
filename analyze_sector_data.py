#!/usr/bin/env python3
"""
分析股票概念数据
"""

import pandas as pd
from sqlalchemy import create_engine, text
from collections import Counter
from config import Config

def analyze_sector_data():
    """分析sector字段中的概念数据"""
    try:
        engine = create_engine(Config.DATABASE_URL)
        
        with engine.connect() as conn:
            # 查看stock_daily_data表结构
            print("📊 查看stock_daily_data表结构...")
            result = conn.execute(text("DESCRIBE stock_daily_data"))
            print("表结构：")
            for row in result.fetchall():
                print(f"  {row[0]} - {row[1]}")
            
            # 查看sector字段的样本数据
            print("\n📊 查看sector字段样本数据...")
            result = conn.execute(text("""
                SELECT stock_code, sector 
                FROM stock_daily_data 
                WHERE sector IS NOT NULL AND sector != '' 
                LIMIT 10
            """))
            
            print("样本数据：")
            for row in result.fetchall():
                print(f"  {row[0]}: {row[1]}")
            
            # 获取所有sector数据
            print("\n📊 获取所有sector数据进行分析...")
            result = conn.execute(text("""
                SELECT sector 
                FROM stock_daily_data 
                WHERE sector IS NOT NULL AND sector != ''
            """))
            
            sectors = [row[0] for row in result.fetchall()]
            print(f"总共获取到 {len(sectors)} 条sector数据")
            
            # 分析概念
            all_concepts = []
            for sector in sectors:
                if sector:
                    # 按"、"分隔概念
                    concepts = [concept.strip() for concept in sector.split('、') if concept.strip()]
                    all_concepts.extend(concepts)
            
            print(f"总共提取到 {len(all_concepts)} 个概念")
            
            # 统计概念出现次数
            concept_counter = Counter(all_concepts)
            
            # 显示前20个最热门的概念
            print("\n🔥 前20个最热门的概念：")
            top_20 = concept_counter.most_common(20)
            for i, (concept, count) in enumerate(top_20, 1):
                print(f"  {i:2d}. {concept:<15} - {count:4d} 次")
            
            return concept_counter
            
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        return None

if __name__ == '__main__':
    analyze_sector_data()
