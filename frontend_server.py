#!/usr/bin/env python3
"""
前端静态文件服务器
提供前端HTML、CSS、JS文件服务
"""

import os
import mimetypes
from pathlib import Path
from fastapi import FastAPI, Request
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# 创建FastAPI应用
app = FastAPI(
    title="股票TOP20监控前端",
    description="前端静态文件服务器",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 前端文件目录
FRONTEND_DIR = Path(__file__).parent / "frontend"
PUBLIC_DIR = FRONTEND_DIR / "public"
SRC_DIR = FRONTEND_DIR / "src"

# 挂载静态文件
app.mount("/src", StaticFiles(directory=str(SRC_DIR)), name="src")

@app.get("/")
async def read_index():
    """返回主页"""
    index_file = PUBLIC_DIR / "index.html"
    if index_file.exists():
        return FileResponse(str(index_file))
    else:
        return HTMLResponse("""
        <html>
            <head><title>文件未找到</title></head>
            <body>
                <h1>前端文件未找到</h1>
                <p>请确保前端文件已正确创建在 frontend/public/ 目录下</p>
                <p>当前查找路径: {}</p>
            </body>
        </html>
        """.format(str(index_file)))

@app.get("/{file_path:path}")
async def read_file(file_path: str):
    """返回静态文件"""
    # 尝试从public目录获取文件
    file_full_path = PUBLIC_DIR / file_path
    
    if file_full_path.exists() and file_full_path.is_file():
        return FileResponse(str(file_full_path))
    
    # 如果是根路径或HTML文件，返回index.html（SPA路由支持）
    if not file_path or file_path.endswith('.html') or '.' not in file_path:
        index_file = PUBLIC_DIR / "index.html"
        if index_file.exists():
            return FileResponse(str(index_file))
    
    # 文件未找到
    return HTMLResponse(
        content=f"<h1>404 - 文件未找到</h1><p>请求的文件: {file_path}</p>",
        status_code=404
    )

if __name__ == "__main__":
    import uvicorn
    print("🌐 启动前端服务器...")
    print("📁 前端目录:", str(FRONTEND_DIR))
    print("🔗 访问地址: http://127.0.0.1:3000")
    print("📋 后端API: http://127.0.0.1:8000")
    uvicorn.run("frontend_server:app", host="127.0.0.1", port=3000, reload=True)
