#!/usr/bin/env python3
"""
股票TOP20监控后端API
纯后端API服务，不包含前端页面
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# JWT配置
SECRET_KEY = "your-secret-key-here-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# 创建FastAPI应用
app = FastAPI(
    title="股票TOP20监控API",
    description="股票TOP20监控系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS - 允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库连接
engine = create_engine(Config.DATABASE_URL)

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class UserInfo(BaseModel):
    username: str
    role: str

class StockData(BaseModel):
    rank: int
    stock_code: str
    stock_name: str
    current_price: float
    change_pct: float
    entry_date: str
    concepts: str
    entry_price: Optional[float] = None

class Top20Response(BaseModel):
    stocks: List[StockData]
    update_time: str
    total_count: int
    positive_count: int
    avg_change: float
    max_gain: float

# 用户验证（简单的硬编码用户）
USERS_DB = {
    "admin": {
        "username": "admin",
        "hashed_password": pwd_context.hash("admin123"),
        "role": "admin"
    },
    "user": {
        "username": "user", 
        "hashed_password": pwd_context.hash("user123"),
        "role": "user"
    }
}

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """验证用户"""
    user = USERS_DB.get(username)
    if not user:
        return None
    if not verify_password(password, user["hashed_password"]):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = USERS_DB.get(username)
    if user is None:
        raise credentials_exception
    return user

def get_top20_from_cache() -> List[Dict]:
    """从缓存表获取TOP20数据"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT rank_num, stock_code, stock_name, current_price, change_pct,
                       entry_date, concepts, entry_price, update_time
                FROM top20_cache 
                ORDER BY rank_num
                LIMIT 20
            """))
            
            rows = result.fetchall()
            stocks = []
            for row in rows:
                stocks.append({
                    'rank': row[0],
                    'stock_code': row[1],
                    'stock_name': row[2],
                    'current_price': float(row[3]),
                    'change_pct': float(row[4]),
                    'entry_date': str(row[5]),
                    'concepts': row[6] or '',
                    'entry_price': float(row[7]) if row[7] else 0.0,
                    'update_time': str(row[8])
                })
            
            logger.info(f"从缓存表获取到 {len(stocks)} 个TOP20股票")
            return stocks
            
    except Exception as e:
        logger.error(f"从缓存表获取TOP20数据失败: {str(e)}")
        return []

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "股票TOP20监控API",
        "version": "1.0.0",
        "docs": "/docs",
        "frontend": "请访问前端应用查看界面"
    }

@app.post("/api/login", response_model=Token)
async def login(login_data: LoginRequest):
    """用户登录"""
    user = authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/user/me", response_model=UserInfo)
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return UserInfo(
        username=current_user["username"],
        role=current_user["role"]
    )

@app.get("/api/top20", response_model=Top20Response)
async def get_top20_stocks(current_user: dict = Depends(get_current_user)):
    """获取TOP20股票数据"""
    try:
        # 从缓存表获取数据
        cached_stocks = get_top20_from_cache()
        
        if not cached_stocks:
            return Top20Response(
                stocks=[],
                update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                total_count=0,
                positive_count=0,
                avg_change=0.0,
                max_gain=0.0
            )
        
        # 构建响应数据
        stocks_with_rank = []
        for stock in cached_stocks:
            stocks_with_rank.append(StockData(
                rank=stock['rank'],
                stock_code=stock['stock_code'],
                stock_name=stock['stock_name'],
                current_price=stock['current_price'],
                change_pct=stock['change_pct'],
                entry_date=stock['entry_date'],
                concepts=stock['concepts'],
                entry_price=stock['entry_price']
            ))
        
        # 计算统计数据
        positive_count = len([s for s in cached_stocks if s['change_pct'] > 0])
        avg_change = sum(s['change_pct'] for s in cached_stocks) / len(cached_stocks) if cached_stocks else 0
        max_gain = max(s['change_pct'] for s in cached_stocks) if cached_stocks else 0
        update_time = cached_stocks[0]['update_time'] if cached_stocks else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return Top20Response(
            stocks=stocks_with_rank,
            update_time=update_time,
            total_count=len(cached_stocks),
            positive_count=positive_count,
            avg_change=round(avg_change, 2),
            max_gain=round(max_gain, 2)
        )
        
    except Exception as e:
        logger.error(f"获取TOP20数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@app.get("/api/health")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": "connected"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "database": "disconnected",
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("backend_api:app", host="127.0.0.1", port=8000, reload=True)
