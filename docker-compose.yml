version: '3.8'

services:
  stock-scheduler:
    build: .
    container_name: stock-task-scheduler
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    volumes:
      - ./logs:/app/logs
      - ./config.py:/app/config.py
    networks:
      - stock-network
#    depends_on:
#      - mysql
    command: python task_scheduler.py

  # 可选：如果需要本地MySQL数据库
#  mysql:
#    image: mysql:8.0
#    container_name: stock-mysql
#    restart: unless-stopped
#    environment:
#      MYSQL_ROOT_PASSWORD: root
#      MYSQL_DATABASE: data_base
#      MYSQL_USER: root
#      MYSQL_PASSWORD: root
#    ports:
#      - "3306:3306"
#    volumes:
#      - mysql_data:/var/lib/mysql
#      - ./mysql/init:/docker-entrypoint-initdb.d
#    networks:
#      - stock-network
#    command: --default-authentication-plugin=mysql_native_password

networks:
  stock-network:
    driver: bridge

volumes:
  mysql_data:
