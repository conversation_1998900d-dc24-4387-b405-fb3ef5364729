#!/usr/bin/env python3
"""
股票数据处理任务调度器
每个交易日凌晨2点自动执行数据处理流程
"""

import os
import sys
import time
import json
import logging
import requests
import subprocess
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import schedule
import pandas as pd
from sqlalchemy import create_engine, text
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('task_scheduler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DingTalkNotifier:
    """钉钉通知器"""
    
    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
    
    def send_message(self, title: str, content: str, is_error: bool = False):
        """发送钉钉消息"""
        try:
            # 构建消息内容
            emoji = "❌" if is_error else "✅"
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": f"{emoji} {title}",
                    "text": f"## {emoji} {title}\n\n{content}\n\n**时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                }
            }
            
            # 发送请求
            response = requests.post(
                self.webhook_url,
                json=message,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    logger.info(f"钉钉消息发送成功: {title}")
                    return True
                else:
                    logger.error(f"钉钉消息发送失败: {result}")
                    return False
            else:
                logger.error(f"钉钉请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"发送钉钉消息异常: {str(e)}")
            return False

class TaskExecutor:
    """任务执行器"""
    
    def __init__(self, notifier: DingTalkNotifier):
        self.notifier = notifier
        self.engine = create_engine(Config.DATABASE_URL)
        
        # 任务配置
        self.tasks = [
            {
                'name': '获取交易日期',
                'script': 'get_trading_dates.py',
                'description': '更新交易日历数据'
            },
            {
                'name': '获取股票基础数据',
                'script': 'get_stock_data_by_day.py',
                'description': '获取股票基础信息和日线数据'
            },
            {
                'name': '同步K线数据',
                'script': 'sync_kline_by_concepts.py',
                'description': '基于热门概念同步K线数据'
            },
            {
                'name': '策略筛选',
                'script': 'new_strategy_screener.py',
                'description': '执行新策略筛选并保存结果'
            }
        ]
    
    def is_trading_day(self, date: datetime = None) -> bool:
        """检查是否为交易日"""
        try:
            if date is None:
                date = datetime.now()

            # 检查是否为周末
            if date.weekday() >= 5:  # 周六=5, 周日=6
                logger.info(f"{date.strftime('%Y-%m-%d %A')} 是周末，跳过执行")
                return False

            # 从stock_market_data表检查是否为交易日
            try:
                with self.engine.connect() as conn:
                    # 检查stock_market_data表中是否有该日期的数据
                    # trade_day字段是varchar类型，格式为YYYY-MM-DD
                    date_str = date.strftime('%Y-%m-%d')
                    result = conn.execute(text("""
                        SELECT COUNT(*) as count
                        FROM stock_market_data
                        WHERE trade_day = :date_str
                    """), {'date_str': date_str})

                    row = result.fetchone()
                    has_data = row[0] > 0 if row else False

                    if has_data:
                        logger.info(f"{date.strftime('%Y-%m-%d')} 是交易日（stock_market_data表中有数据）")
                        return True
                    else:
                        # 如果stock_market_data表中没有数据，检查是否是最近的日期
                        # 如果是今天或未来日期，且不是周末，则认为是交易日
                        today = datetime.now().date()
                        if date.date() >= today and date.weekday() < 5:
                            logger.info(f"{date.strftime('%Y-%m-%d')} 是交易日（今日或未来工作日，默认为交易日）")
                            return True
                        else:
                            logger.info(f"{date.strftime('%Y-%m-%d')} 不是交易日（stock_market_data表中无数据且非今日工作日）")
                            return False

            except Exception as db_error:
                logger.warning(f"数据库查询交易日失败: {str(db_error)}")
                # 数据库查询失败，使用默认逻辑
                is_default_trading = date.weekday() < 5
                logger.info(f"使用默认判断: {date.strftime('%Y-%m-%d %A')} {'是' if is_default_trading else '不是'}交易日")
                return is_default_trading

        except Exception as e:
            logger.warning(f"交易日检查异常: {str(e)}")
            # 如果检查失败，默认非周末就执行
            is_default_trading = date.weekday() < 5
            logger.info(f"异常情况使用默认判断: {'执行' if is_default_trading else '跳过'}")
            return is_default_trading
    
    def execute_script(self, script_name: str, timeout: int = 9800, args: List[str] = None) -> Dict:
        """执行Python脚本"""
        try:
            logger.info(f"开始执行脚本: {script_name}")
            start_time = time.time()

            # 构建命令
            cmd = [sys.executable, script_name]
            if args:
                cmd.extend(args)

            # 执行脚本
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.getcwd()
            )

            end_time = time.time()
            duration = end_time - start_time

            # 打印脚本的输出日志
            if result.stdout:
                logger.info(f"脚本 {script_name} 输出:")
                for line in result.stdout.strip().split('\n'):
                    if line.strip():
                        logger.info(f"  {line}")

            if result.stderr:
                logger.warning(f"脚本 {script_name} 错误输出:")
                for line in result.stderr.strip().split('\n'):
                    if line.strip():
                        logger.warning(f"  {line}")

            # 返回执行结果
            return {
                'success': result.returncode == 0,
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'duration': duration
            }

        except subprocess.TimeoutExpired:
            logger.error(f"脚本 {script_name} 执行超时")
            return {
                'success': False,
                'returncode': -1,
                'stdout': '',
                'stderr': f'脚本执行超时 (>{timeout}秒)',
                'duration': timeout
            }
        except Exception as e:
            logger.error(f"执行脚本 {script_name} 异常: {str(e)}")
            return {
                'success': False,
                'returncode': -1,
                'stdout': '',
                'stderr': str(e),
                'duration': 0
            }
    
    def get_latest_strategy_results(self) -> List[Dict]:
        """获取最新的策略结果"""
        try:
            with self.engine.connect() as conn:
                # 获取今天的策略结果
                today = datetime.now().date()
                result = conn.execute(text("""
                    SELECT stock_code, stock_name, concepts, entry_date, entry_price,
                           pullback_ratio, distance_to_high, time_gap_days
                    FROM new_strategy_results 
                    WHERE DATE(created_at) = :today
                    ORDER BY created_at DESC
                    LIMIT 10
                """), {'today': today})
                
                rows = result.fetchall()
                
                results = []
                for row in rows:
                    results.append({
                        'stock_code': row[0],
                        'stock_name': row[1],
                        'concepts': row[2],
                        'entry_date': row[3],
                        'entry_price': row[4],
                        'pullback_ratio': row[5],
                        'distance_to_high': row[6],
                        'time_gap_days': row[7]
                    })
                
                return results
                
        except Exception as e:
            logger.error(f"获取策略结果失败: {str(e)}")
            return []
    
    def format_strategy_results(self, results: List[Dict]) -> str:
        """格式化策略结果为钉钉消息"""
        if not results:
            return "今日暂无新的策略信号"
        
        content = f"**今日策略信号数量**: {len(results)}\n\n"
        
        for i, result in enumerate(results[:5], 1):  # 只显示前5个
            content += f"**{i}. {result['stock_code']} - {result['stock_name']}**\n"
            content += f"- 概念: {result['concepts']}\n"
            content += f"- 入选价格: {result['entry_price']:.2f}元\n"
            content += f"- 回调幅度: {result['pullback_ratio']:.1f}%\n"
            content += f"- 距离前高: {result['distance_to_high']:.1f}%\n\n"
        
        if len(results) > 5:
            content += f"... 还有 {len(results) - 5} 个信号\n\n"
        
        return content

    def is_trading_time(self) -> bool:
        """检查当前是否为交易时间"""
        now = datetime.now()
        current_time = now.time()

        # 检查是否为交易日
        if not self.is_trading_day(now):
            return False

        # 交易时间段：9:30-11:30 和 13:00-15:00
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()

        is_morning = morning_start <= current_time <= morning_end
        is_afternoon = afternoon_start <= current_time <= afternoon_end

        return is_morning or is_afternoon

    def run_top20_monitor(self):
        """执行TOP20股票监控任务"""
        try:
            # 检查是否为交易时间
            if not self.is_trading_time():
                logger.debug("当前非交易时间，跳过TOP20监控")
                return

            logger.info("开始执行TOP20股票监控")

            # 执行TOP20监控脚本，传入参数15（最近15个交易日）
            result = self.execute_script('send_top20_to_dingtalk.py', timeout=300, args=['15'])

            if result['success']:
                logger.info(f"✅ TOP20监控执行成功 (耗时: {result['duration']:.1f}秒)")
            else:
                logger.error(f"❌ TOP20监控执行失败")
                logger.error(f"错误信息: {result['stderr']}")

        except Exception as e:
            logger.error(f"TOP20监控任务异常: {str(e)}")

    def run_daily_tasks(self):
        """执行每日任务流程"""
        try:
            logger.info("=" * 80)
            logger.info("开始执行每日任务流程")
            logger.info("=" * 80)
            
            # 检查是否为交易日
            if not self.is_trading_day():
                logger.info("今日非交易日，跳过任务执行")
                return
            
            # 发送开始通知
            self.notifier.send_message(
                "每日任务开始",
                f"开始执行每日股票数据处理任务\n\n**任务列表**:\n" + 
                "\n".join([f"{i+1}. {task['name']}" for i, task in enumerate(self.tasks)])
            )
            
            # 执行任务序列
            all_success = True
            task_results = []
            
            for i, task in enumerate(self.tasks):
                logger.info(f"[{i+1}/{len(self.tasks)}] 执行任务: {task['name']}")
                
                # 执行脚本
                result = self.execute_script(task['script'])
                task_results.append({
                    'task': task,
                    'result': result
                })
                
                if result['success']:
                    logger.info(f"✅ 任务 {task['name']} 执行成功 (耗时: {result['duration']:.1f}秒)")
                else:
                    logger.error(f"❌ 任务 {task['name']} 执行失败")
                    all_success = False
                    
                    # 发送失败通知
                    error_content = f"**任务**: {task['name']}\n"
                    error_content += f"**脚本**: {task['script']}\n"
                    error_content += f"**错误码**: {result['returncode']}\n"
                    error_content += f"**错误信息**: \n```\n{result['stderr']}\n```"
                    
                    self.notifier.send_message(
                        f"任务执行失败: {task['name']}",
                        error_content,
                        is_error=True
                    )
                    
                    # 如果任务失败，停止后续任务
                    break
            
            # 如果所有任务都成功，发送策略结果
            if all_success:
                logger.info("✅ 所有任务执行成功")
                
                # 获取并发送策略结果
                strategy_results = self.get_latest_strategy_results()
                strategy_content = self.format_strategy_results(strategy_results)
                
                # 生成任务执行摘要
                summary = "**任务执行摘要**:\n\n"
                for i, task_result in enumerate(task_results):
                    task = task_result['task']
                    result = task_result['result']
                    summary += f"{i+1}. {task['name']}: ✅ 成功 ({result['duration']:.1f}秒)\n"
                
                summary += f"\n{strategy_content}"
                
                self.notifier.send_message(
                    "每日任务完成",
                    summary
                )
            else:
                logger.error("❌ 任务执行过程中出现失败")
            
            logger.info("=" * 80)
            logger.info("每日任务流程结束")
            logger.info("=" * 80)
            
        except Exception as e:
            logger.error(f"执行每日任务异常: {str(e)}")
            self.notifier.send_message(
                "任务调度器异常",
                f"任务调度器执行过程中发生异常:\n\n```\n{str(e)}\n```",
                is_error=True
            )

class TaskScheduler:
    """任务调度器"""
    
    def __init__(self):
        # 钉钉Webhook URL
        self.webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55"
        
        # 初始化组件
        self.notifier = DingTalkNotifier(self.webhook_url)
        self.executor = TaskExecutor(self.notifier)
    
    def start(self):
        """启动任务调度器"""
        try:
            logger.info("🚀 任务调度器启动")

            # 调度每日任务：每天凌晨7点30执行
            schedule.every().day.at("00:10").do(self.executor.run_daily_tasks)

            # 调度TOP20监控任务：交易时间内每分钟执行一次
            schedule.every().minute.do(self.executor.run_top20_monitor)

            # 发送启动通知
            self.notifier.send_message(
                "任务调度器启动",
                "股票数据处理任务调度器已启动\n\n**每日任务**: 每日凌晨7:30\n**TOP20监控**: 交易时间内每分钟\n**交易时间**: 9:30-11:30, 13:00-15:00"
            )

            logger.info("📅 任务已调度：")
            logger.info("  - 每日任务：每日凌晨00:10执行")
            logger.info("  - TOP20监控：交易时间内每分钟执行")
            logger.info("⏰ 等待调度时间...")

            # 运行调度器
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("⚠️ 用户中断，任务调度器停止")
            self.notifier.send_message(
                "任务调度器停止",
                "任务调度器已手动停止"
            )
        except Exception as e:
            logger.error(f"任务调度器异常: {str(e)}")
            self.notifier.send_message(
                "任务调度器异常",
                f"任务调度器发生异常:\n\n```\n{str(e)}\n```",
                is_error=True
            )

def main():
    """主函数"""
    print("🚀 股票数据处理任务调度器")
    print("=" * 60)
    print("功能1：每个交易日凌晨7:30自动执行数据处理流程")
    print("任务：交易日期 → 股票数据 → K线同步 → 策略筛选")
    print("")
    print("功能2：交易时间内每分钟执行TOP20股票监控")
    print("时间：9:30-11:30, 13:00-15:00")
    print("任务：获取策略股票实时行情并推送TOP20到钉钉")
    print("")
    print("通知：钉钉机器人推送执行结果")
    print("=" * 60)
    
    # 启动调度器
    scheduler = TaskScheduler()
    scheduler.start()

if __name__ == '__main__':
    main()
