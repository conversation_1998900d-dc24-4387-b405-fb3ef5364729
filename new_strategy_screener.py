#!/usr/bin/env python3
"""
新股票策略筛选器
策略思路：
1. 从最高点到最低点回调超过25个点
2. 然后现在即将突破前高(相差10-15个点就能突破前高)
3. 和前高至少相差1个月以上
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text, Column, Integer, String, Float, Date, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('new_strategy_screener.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置
engine = create_engine(Config.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 新策略结果表模型
class NewStrategyResult(Base):
    __tablename__ = 'new_strategy_results'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(20), nullable=False)
    stock_name = Column(String(50), nullable=True)  # 股票名称
    concepts = Column(String(500), nullable=True)  # 所属概念（去重后）
    entry_date = Column(Date, nullable=False)  # 入选日期
    entry_price = Column(Float, nullable=False)  # 入选价格
    previous_high_date = Column(Date, nullable=False)  # 前高日期
    previous_high_price = Column(Float, nullable=False)  # 前高价格
    lowest_point_date = Column(Date, nullable=False)  # 最低点日期
    lowest_point_price = Column(Float, nullable=False)  # 最低点价格
    pullback_ratio = Column(Float, nullable=False)  # 回调幅度
    distance_to_high = Column(Float, nullable=False)  # 距离前高的点数
    distance_to_high_ratio = Column(Float, nullable=False)  # 距离前高的百分比
    time_gap_days = Column(Integer, nullable=False)  # 与前高的时间间隔（天）
    current_ma5 = Column(Float, nullable=True)  # MA5
    current_ma10 = Column(Float, nullable=True)  # MA10
    volume = Column(Float, nullable=True)  # 成交量
    turnover_rate = Column(Float, nullable=True)  # 换手率
    created_at = Column(DateTime, default=datetime.now)  # 创建时间
    
    def __repr__(self):
        return f"<NewStrategyResult(stock_code='{self.stock_code}', entry_date='{self.entry_date}')>"

class NewStrategyScreener:
    """新策略筛选器"""
    
    def __init__(self):
        self.session = SessionLocal()
        
    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()
    
    def create_strategy_table(self):
        """创建策略结果表"""
        try:
            # 先删除现有表（如果存在）
            Base.metadata.drop_all(engine, tables=[NewStrategyResult.__table__])
            logger.info("删除现有新策略结果表")

            # 重新创建表
            Base.metadata.create_all(engine)
            logger.info("新策略结果表创建成功")
        except Exception as e:
            logger.error(f"创建新策略结果表失败: {str(e)}")
    
    def get_stock_data(self, stock_code, days=180):
        """获取指定股票的K线数据"""
        try:
            # 计算起始日期
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            query = text("""
                SELECT stock_code, trade_date, open_price, high_price, low_price, close_price,
                       volume, amount, turnover_rate
                FROM stock_kline_data 
                WHERE stock_code = :stock_code 
                AND trade_date >= :start_date
                ORDER BY trade_date ASC
            """)
            
            result = self.session.execute(query, {
                'stock_code': stock_code,
                'start_date': start_date
            })
            
            df = pd.DataFrame(result.fetchall(), columns=[
                'stock_code', 'trade_date', 'open_price', 'high_price', 'low_price', 'close_price',
                'volume', 'amount', 'turnover_rate'
            ])
            
            if not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date').reset_index(drop=True)
                
                # 计算涨跌幅
                df['pct_change'] = df['close_price'].pct_change() * 100
                
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 数据失败: {str(e)}")
            return pd.DataFrame()

    def get_stock_info(self, stock_code):
        """获取股票名称和概念信息"""
        try:
            query = text("""
                SELECT stock_name, sector, trade_day
                FROM stock_daily_data
                WHERE stock_code = :stock_code
                AND sector IS NOT NULL AND sector != ''
                ORDER BY trade_day DESC
            """)

            result = self.session.execute(query, {'stock_code': stock_code})
            rows = result.fetchall()

            if not rows:
                return None, None

            # 获取最新的股票名称
            stock_name = rows[0][0]

            # 收集所有概念并去重
            all_concepts = set()
            seen_sectors = set()  # 避免重复处理相同的sector
            for row in rows:
                if row[1] and row[1] not in seen_sectors:  # sector字段不为空且未处理过
                    seen_sectors.add(row[1])
                    concepts = [concept.strip() for concept in row[1].split('、') if concept.strip()]
                    all_concepts.update(concepts)

            # 将概念列表转换为字符串
            concepts_str = '、'.join(sorted(all_concepts)) if all_concepts else ''

            return stock_name, concepts_str

        except Exception as e:
            logger.error(f"获取股票 {stock_code} 信息失败: {str(e)}")
            return None, None
    
    def check_strategy_conditions(self, df, stock_code):
        """检查新策略条件"""
        if df.empty or len(df) < 60:  # 至少需要60天数据
            return None
        
        # 遍历每一天，检查是否符合策略
        for i in range(60, len(df)):  # 从第60天开始检查
            current_date = df.iloc[i]['trade_date']
            current_price = df.iloc[i]['close_price']
            
            # 获取当前日期前的数据
            history_df = df.iloc[:i+1].copy()
            
            # 条件1：找到前高（至少30天前的最高点）
            previous_high_result = self._find_previous_high(history_df, i)
            if not previous_high_result:
                continue
            
            # 条件2：检查回调幅度是否超过25个点
            pullback_result = self._check_major_pullback(history_df, i, previous_high_result)
            if not pullback_result:
                continue
            
            # 条件3：检查是否即将突破前高（相差10-15个点）
            breakthrough_result = self._check_near_breakthrough(history_df, i, previous_high_result)
            if not breakthrough_result:
                continue
            
            # 条件4：检查与前高的时间间隔是否超过1个月
            time_gap_result = self._check_time_gap(previous_high_result, current_date)
            if not time_gap_result:
                continue
            
            # 获取股票名称和概念信息
            stock_name, concepts = self.get_stock_info(stock_code)

            # 如果所有条件都满足，返回第一次符合条件的结果
            result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'concepts': concepts,
                'entry_date': current_date.strftime('%Y-%m-%d'),
                'entry_price': current_price,
                'previous_high_date': previous_high_result['high_date'].strftime('%Y-%m-%d'),
                'previous_high_price': previous_high_result['high_price'],
                'lowest_point_date': pullback_result['low_date'].strftime('%Y-%m-%d'),
                'lowest_point_price': pullback_result['low_price'],
                'pullback_ratio': pullback_result['pullback_ratio'],
                'distance_to_high': breakthrough_result['distance_points'],
                'distance_to_high_ratio': breakthrough_result['distance_ratio'],
                'time_gap_days': time_gap_result['gap_days'],

                'volume': df.iloc[i]['volume'],
                'turnover_rate': df.iloc[i]['turnover_rate']
            }
            
            return result
        
        return None
    
    def _find_previous_high(self, df, current_idx, min_days_ago=30):
        """找到前高（至少30天前的最高点）"""
        if current_idx < min_days_ago:
            return None

        # 查找30天前到90天前的最高点
        start_idx = max(0, current_idx - 90)
        end_idx = current_idx - min_days_ago

        if end_idx <= start_idx:
            return None

        period_df = df.iloc[start_idx:end_idx]
        if period_df.empty:
            return None

        # 找到最高点（使用high_price字段）
        max_price = period_df['high_price'].max()
        max_idx = period_df['high_price'].idxmax()
        max_date = df.iloc[max_idx]['trade_date']

        return {
            'high_idx': max_idx,
            'high_price': max_price,
            'high_date': max_date
        }
    
    def _check_major_pullback(self, df, current_idx, previous_high_result, min_pullback=25):
        """检查从前高到最低点的回调是否超过25个点"""
        high_idx = previous_high_result['high_idx']
        high_price = previous_high_result['high_price']

        # 从前高后开始查找最低点
        after_high_df = df.iloc[high_idx:current_idx+1]
        if after_high_df.empty:
            return None

        # 找到最低点（使用low_price字段）
        min_price = after_high_df['low_price'].min()
        min_idx = after_high_df['low_price'].idxmin()
        min_date = df.iloc[min_idx]['trade_date']

        # 计算回调幅度（百分比）
        pullback_ratio = ((high_price - min_price) / high_price) * 100

        if pullback_ratio >= min_pullback:
            return {
                'low_idx': min_idx,
                'low_price': min_price,
                'low_date': min_date,
                'pullback_ratio': pullback_ratio
            }

        return None
    
    def _check_near_breakthrough(self, df, current_idx, previous_high_result, min_distance=10, max_distance=15):
        """检查是否即将突破前高（相差10-15个点）"""
        # 使用当前的最高价而不是收盘价
        current_high_price = df.iloc[current_idx]['high_price']
        previous_high_price = previous_high_result['high_price']

        # 计算距离前高的百分比
        distance_ratio = ((previous_high_price - current_high_price) / previous_high_price) * 100

        if min_distance <= distance_ratio <= max_distance:
            return {
                'distance_points': distance_ratio,
                'distance_ratio': distance_ratio
            }

        return None
    
    def _check_time_gap(self, previous_high_result, current_date, min_gap_days=30):
        """检查与前高的时间间隔是否超过1个月"""
        high_date = previous_high_result['high_date']
        gap_days = (current_date - high_date).days
        
        if gap_days >= min_gap_days:
            return {
                'gap_days': gap_days
            }
        
        return None

    def save_strategy_result(self, result_data):
        """保存策略结果到数据库"""
        try:
            # 检查是否已存在相同的记录
            existing = self.session.query(NewStrategyResult).filter(
                NewStrategyResult.stock_code == result_data['stock_code'],
                NewStrategyResult.entry_date == result_data['entry_date']
            ).first()

            if existing:
                logger.info(f"股票 {result_data['stock_code']} 在 {result_data['entry_date']} 的策略结果已存在，跳过保存")
                return False

            # 创建新记录
            strategy_result = NewStrategyResult(
                stock_code=result_data['stock_code'],
                stock_name=result_data.get('stock_name'),
                concepts=result_data.get('concepts'),
                entry_date=datetime.strptime(result_data['entry_date'], '%Y-%m-%d').date(),
                entry_price=result_data['entry_price'],
                previous_high_date=datetime.strptime(result_data['previous_high_date'], '%Y-%m-%d').date(),
                previous_high_price=result_data['previous_high_price'],
                lowest_point_date=datetime.strptime(result_data['lowest_point_date'], '%Y-%m-%d').date(),
                lowest_point_price=result_data['lowest_point_price'],
                pullback_ratio=result_data['pullback_ratio'],
                distance_to_high=result_data['distance_to_high'],
                distance_to_high_ratio=result_data['distance_to_high_ratio'],
                time_gap_days=result_data['time_gap_days'],

                volume=result_data['volume'],
                turnover_rate=result_data['turnover_rate']
            )

            self.session.add(strategy_result)
            self.session.commit()
            logger.info(f"✅ 股票 {result_data['stock_code']} 新策略结果保存成功")
            return True

        except Exception as e:
            self.session.rollback()
            logger.error(f"保存新策略结果失败: {str(e)}")
            return False

    def get_all_stock_codes(self):
        """获取所有有K线数据的股票代码"""
        try:
            query = text("""
                SELECT DISTINCT stock_code
                FROM stock_kline_data
                WHERE trade_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                ORDER BY stock_code
            """)

            result = self.session.execute(query)
            stock_codes = [row[0] for row in result.fetchall()]

            logger.info(f"获取到 {len(stock_codes)} 个股票代码")
            return stock_codes

        except Exception as e:
            logger.error(f"获取股票代码失败: {str(e)}")
            return []

    def screen_stocks(self, stock_codes=None, max_stocks=None):
        """筛选符合新策略的股票"""
        if stock_codes is None:
            stock_codes = self.get_all_stock_codes()

        if max_stocks:
            stock_codes = stock_codes[:max_stocks]

        logger.info(f"开始筛选 {len(stock_codes)} 个股票...")

        all_results = []
        processed_count = 0

        for i, stock_code in enumerate(stock_codes):
            try:
                logger.info(f"[{i+1}/{len(stock_codes)}] 分析股票: {stock_code}")

                # 获取股票数据
                df = self.get_stock_data(stock_code)

                if df.empty:
                    logger.warning(f"股票 {stock_code} 无数据，跳过")
                    continue

                # 检查策略条件
                result = self.check_strategy_conditions(df, stock_code)

                if result:
                    all_results.append(result)
                    # 保存到数据库
                    self.save_strategy_result(result)
                    logger.info(f"✅ 股票 {stock_code} 符合新策略")

                processed_count += 1

                # 每处理50个股票显示一次进度
                if processed_count % 50 == 0:
                    logger.info(f"📊 已处理 {processed_count} 个股票，找到 {len(all_results)} 个符合新策略的股票")

            except Exception as e:
                logger.error(f"分析股票 {stock_code} 时发生错误: {str(e)}")
                continue

        logger.info(f"筛选完成！共处理 {processed_count} 个股票，找到 {len(all_results)} 个符合新策略的股票")
        return all_results

    def print_results(self, results):
        """打印筛选结果"""
        if not results:
            print("❌ 未找到符合新策略的股票")
            return

        print("=" * 120)
        print(f"🎯 新策略筛选结果 - 共找到 {len(results)} 个符合策略的股票")
        print("=" * 120)

        # 按入选日期排序
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('entry_date', ascending=False)

        for i, result in results_df.iterrows():
            print(f"\n📈 股票 {i+1}:")
            print(f"   股票代码: {result['stock_code']}")
            if result.get('stock_name'):
                print(f"   股票名称: {result['stock_name']}")
            if result.get('concepts'):
                print(f"   所属概念: {result['concepts']}")
            print(f"   入选日期: {result['entry_date']}")
            print(f"   入选价格: {result['entry_price']:.2f}")
            print(f"   ")
            print(f"   📊 策略关键数据:")
            print(f"   前高价格: {result['previous_high_price']:.2f} (日期: {result['previous_high_date']})")
            print(f"   最低价格: {result['lowest_point_price']:.2f} (日期: {result['lowest_point_date']})")
            print(f"   ")
            print(f"   📉 回调幅度: {result['pullback_ratio']:.1f}%")
            print(f"   📈 距离前高: {result['distance_to_high']:.1f}% ({result['distance_to_high']:.1f}个点)")
            print(f"   📅 时间间隔: {result['time_gap_days']} 天")
            print(f"   ")
            print(f"   📋 技术指标:")
            volume_str = f"{result['volume']:,.0f}" if result['volume'] and not pd.isna(result['volume']) else '0'
            turnover_str = f"{result['turnover_rate']:.2f}%" if result['turnover_rate'] and not pd.isna(result['turnover_rate']) else 'N/A'


            print(f"   成交量: {volume_str}")
            print(f"   换手率: {turnover_str}")
            print("-" * 100)

def main():
    """主函数"""
    print("🚀 新股票策略筛选器启动")
    print("策略说明：")
    print("1. 从最高点到最低点回调超过25个点")
    print("2. 然后现在即将突破前高(相差10-15个点就能突破前高)")
    print("3. 和前高至少相差1个月以上")
    print("4. 每个股票只保留第一次符合策略的数据")
    print("5. 符合策略的股票将自动保存到数据库 new_strategy_results 表中")

    try:
        screener = NewStrategyScreener()

        # 创建策略结果表
        print("📊 初始化数据库表...")
        screener.create_strategy_table()

        # 默认筛选所有股票
        print("\n🚀 开始筛选所有股票...")
        print("💡 这可能需要一些时间，请耐心等待...")
        print("💾 符合策略的股票将自动保存到数据库中...")

        results = screener.screen_stocks()

        # 打印结果
        screener.print_results(results)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行异常: {str(e)}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")

if __name__ == '__main__':
    main()
