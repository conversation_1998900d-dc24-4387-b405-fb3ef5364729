import requests
import json
import time
import random
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, Column, String, Integer, Float, Date, text
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy import inspect
from config import Config

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format=Config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('stock_data.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接信息
engine = create_engine(Config.DATABASE_URL)
Base = declarative_base()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 定义数据模型
class StockDailyData(Base):
    __tablename__ = 'stock_daily_data'

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    trade_day = Column(Date, index=True, comment='交易日期')
    stock_code = Column(String(20), index=True, comment='股票代码')
    stock_name = Column(String(50), comment='股票名称')
    price = Column(Float, comment='价格')
    increase_rate = Column(Float, comment='涨幅')
    sector = Column(String(100), comment='板块')
    main_net_amount = Column(Float, comment='主力净额')
    main_buy = Column(Float, comment='主力买入')
    main_sell = Column(Float, comment='主力卖出')
    turnover = Column(Float, comment='成交额')
    actual_circulation = Column(Float, comment='实际流通')
    actual_turnover_rate = Column(Float, comment='实际换手')
    is_limit_up = Column(Integer, default=1, comment='是否涨停(1-是,0-否)')

class StockMarketData(Base):
    __tablename__ = 'stock_market_data'

    trade_day = Column(String(10), primary_key=True, comment='交易日期')
    strong = Column(Integer, comment='情绪强度')
    ztjs = Column(Integer, comment='涨停家数')
    lbgd = Column(Integer, comment='连板高度')
    df_num = Column(Integer, comment='大幅回撤个数')

# 创建表
def create_table():
    inspector = inspect(engine)

    # 检查stock_daily_data表是否存在
    if not inspector.has_table('stock_daily_data'):
        Base.metadata.create_all(bind=engine)
        logger.info("表 stock_daily_data 创建成功")
    else:
        # 表已存在，检查是否需要添加新字段
        columns = inspector.get_columns('stock_daily_data')
        column_names = [col['name'] for col in columns]

        # 检查是否有is_limit_up字段
        if 'is_limit_up' not in column_names:
            logger.info("检测到表结构需要更新，添加 is_limit_up 字段...")
            try:
                # 使用原生SQL添加字段
                with engine.connect() as conn:
                    conn.execute(text("ALTER TABLE stock_daily_data ADD COLUMN is_limit_up INT DEFAULT 1 COMMENT '是否涨停(1-是,0-否)'"))
                    conn.commit()
                logger.info("成功添加 is_limit_up 字段")
            except Exception as e:
                logger.error(f"添加字段失败: {str(e)}")
        else:
            logger.info("表 stock_daily_data 已存在且结构正确")

    # 检查stock_market_data表是否存在
    if not inspector.has_table('stock_market_data'):
        Base.metadata.create_all(bind=engine)
        logger.info("表 stock_market_data 创建成功")
    else:
        logger.info("表 stock_market_data 已存在")

# 获取股票数据
def get_stock_data(date_str):
    """
    获取指定日期的涨停股票数据

    Args:
        date_str: 日期字符串，格式为 'YYYY-MM-DD'

    Returns:
        list: 股票数据列表，如果获取失败返回None
    """
    # 使用配置文件中的参数
    params = Config.get_api_params(date_str)
    # 优先使用PostmanRuntime User-Agent，因为测试证明它最有效
    user_agent = Config.USER_AGENTS[0]  # 使用第一个（PostmanRuntime）
    headers = Config.get_headers(user_agent)

    logger.info(f"开始获取 {date_str} 的股票数据")
    logger.debug(f"请求URL: {Config.BASE_URL}")
    logger.debug(f"请求参数: {params}")

    # 重试机制
    for attempt in range(Config.MAX_RETRIES):
        try:
            # 创建session对象来保持会话
            session = requests.Session()

            # 添加随机延迟，避免请求过于频繁
            if attempt > 0:
                delay = random.uniform(Config.RETRY_DELAY_MIN, Config.RETRY_DELAY_MAX)
                logger.info(f"第{attempt + 1}次尝试，等待 {delay:.2f} 秒...")
                time.sleep(delay)

            # 发送请求
            response = session.get(Config.BASE_URL, params=params, headers=headers, timeout=Config.REQUEST_TIMEOUT)

            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应头: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    logger.debug(f"API响应: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")

                    if data.get('errcode') == '0':
                        info_data = data.get('info', [])
                        if info_data and len(info_data) > 0:
                            # info_data[0] 是股票列表，info_data[1] 可能是其他信息
                            stock_list = info_data[0] if isinstance(info_data[0], list) else []
                            stock_count = len(stock_list)
                            logger.info(f"成功获取到 {stock_count} 条股票数据")
                            if stock_count > 0:
                                logger.debug(f"第一条股票数据字段数: {len(stock_list[0]) if stock_list[0] else 0}")
                                logger.debug(f"第一条股票数据: {stock_list[0] if stock_list else 'None'}")
                            return info_data
                        else:
                            logger.warning("API返回成功但数据为空")
                            return None
                    else:
                        logger.error(f"API返回错误: {data.get('errcode')}, 错误信息: {data.get('errmsg')}")
                        if attempt < Config.MAX_RETRIES - 1:
                            continue
                        return None

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {str(e)}")
                    logger.debug(f"响应内容前500字符: {response.text[:500]}")
                    if attempt < Config.MAX_RETRIES - 1:
                        continue
                    return None

            elif response.status_code == 429:
                # 请求过于频繁，增加延迟
                delay = random.uniform(Config.RATE_LIMIT_DELAY_MIN, Config.RATE_LIMIT_DELAY_MAX)
                logger.warning(f"请求过于频繁(429)，等待 {delay:.2f} 秒后重试...")
                time.sleep(delay)
                continue

            elif response.status_code in [403, 401]:
                logger.error(f"访问被拒绝(状态码: {response.status_code})，可能需要登录或权限")
                return None

            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                logger.debug(f"响应内容: {response.text[:200]}")
                if attempt < Config.MAX_RETRIES - 1:
                    continue
                return None

        except requests.exceptions.Timeout:
            logger.warning(f"请求超时，第{attempt + 1}次尝试")
            if attempt < Config.MAX_RETRIES - 1:
                continue
            return None

        except requests.exceptions.ConnectionError:
            logger.warning(f"连接错误，第{attempt + 1}次尝试")
            if attempt < Config.MAX_RETRIES - 1:
                time.sleep(random.uniform(5, 10))
                continue
            return None

        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            if attempt < Config.MAX_RETRIES - 1:
                continue
            return None

    logger.error(f"经过 {Config.MAX_RETRIES} 次尝试后仍然失败")
    return None

# 存储数据到数据库
def save_data_to_db(data, date_str):
    """
    将股票数据保存到数据库

    Args:
        data: API返回的股票数据
        date_str: 日期字符串
    """
    if not data or not isinstance(data, list) or len(data) < 1:
        logger.error("数据格式不正确或为空")
        return False

    trade_day = datetime.strptime(date_str, '%Y-%m-%d').date()
    stock_list = data[0] if data[0] else []

    if not stock_list:
        logger.warning(f"{date_str} 没有股票数据")
        return False

    session = SessionLocal()

    try:
        # 先删除当天已存在的数据
        deleted_count = session.query(StockDailyData).filter(StockDailyData.trade_day == trade_day).delete()
        if deleted_count > 0:
            logger.info(f"删除了 {deleted_count} 条已存在的 {date_str} 数据")

        saved_count = 0
        for i, stock in enumerate(stock_list):
            if len(stock) < Config.MIN_STOCK_DATA_FIELDS:
                logger.warning(f"第{i+1}条股票数据不完整，字段数: {len(stock)}, 数据: {stock}")
                continue

            try:
                stock_data = StockDailyData(
                    trade_day=trade_day,
                    stock_code=stock[0],
                    stock_name=stock[1],
                    price=float(stock[4]) if stock[4] else 0.0,
                    increase_rate=float(stock[5]) if stock[5] else 0.0,
                    sector=stock[6] if stock[6] else '',
                    main_net_amount=float(stock[7]) if stock[7] else 0.0,
                    main_buy=float(stock[8]) if stock[8] else 0.0,
                    main_sell=float(stock[9]) if stock[9] else 0.0,
                    turnover=float(stock[10]) if stock[10] else 0.0,
                    actual_circulation=float(stock[12]) if len(stock) > 12 and stock[12] else 0.0,
                    actual_turnover_rate=float(stock[14]) if len(stock) > 14 and stock[14] else 0.0,
                    is_limit_up=1  # 这个接口下载的数据默认为涨停股票
                )
                session.add(stock_data)
                saved_count += 1
            except (ValueError, IndexError) as e:
                logger.warning(f"处理第{i+1}条股票数据时出错: {str(e)}, 数据: {stock}")
                continue

        session.commit()
        logger.info(f"成功保存 {saved_count} 条数据到数据库，日期: {date_str}")
        return True

    except Exception as e:
        session.rollback()
        logger.error(f"保存数据异常: {str(e)}")
        return False
    finally:
        session.close()

# 获取最新交易日期
def get_latest_trading_date():
    """获取数据库中最新的交易日期"""
    session = SessionLocal()
    try:
        latest_date = session.query(StockDailyData.trade_day).order_by(StockDailyData.trade_day.desc()).first()
        if latest_date:
            logger.info(f"数据库中最新交易日期: {latest_date[0]}")
            return latest_date[0]
        else:
            # 如果没有数据，返回30天前的日期
            default_date = (datetime.now() - timedelta(days=30)).date()
            logger.info(f"数据库中无数据，返回默认日期: {default_date}")
            return default_date
    except Exception as e:
        logger.error(f"获取最新交易日期异常: {str(e)}")
        return (datetime.now() - timedelta(days=30)).date()
    finally:
        session.close()

# 检查是否为交易日（简单版本，可以根据需要完善）
def is_trading_day(date_obj):
    """
    简单判断是否为交易日（排除周末）
    实际使用中可以接入更完整的交易日历API
    """
    return date_obj.weekday() < 5  # 0-4为周一到周五

# 获取日期范围内的所有交易日
def get_trading_days_in_range(start_date, end_date):
    """
    获取指定日期范围内的所有交易日
    """
    trading_days = []
    current_date = start_date

    while current_date <= end_date:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date += timedelta(days=1)

    return trading_days

# 获取stock_market_data表中的所有交易日期
def get_market_data_dates():
    """获取stock_market_data表中的所有交易日期"""
    session = SessionLocal()
    try:
        dates = session.query(StockMarketData.trade_day).all()
        return [date[0] for date in dates]
    except Exception as e:
        logger.error(f"获取市场数据日期失败: {str(e)}")
        return []
    finally:
        session.close()

# 检查stock_daily_data表中是否已有指定日期的数据
def has_daily_data(date_str):
    """检查stock_daily_data表中是否已有指定日期的数据"""
    session = SessionLocal()
    try:
        trade_day = datetime.strptime(date_str, '%Y-%m-%d').date()
        count = session.query(StockDailyData).filter(StockDailyData.trade_day == trade_day).count()
        return count > 0
    except Exception as e:
        logger.error(f"检查日期数据失败: {str(e)}")
        return False
    finally:
        session.close()

# 获取需要同步的日期列表
def get_dates_to_sync():
    """获取需要同步的日期列表（在market_data中有但在daily_data中没有的日期）"""
    market_dates = get_market_data_dates()
    dates_to_sync = []

    for date_str in market_dates:
        if not has_daily_data(date_str):
            dates_to_sync.append(date_str)

    # 按日期排序
    dates_to_sync.sort()
    return dates_to_sync

# 自动同步数据
def auto_sync_data():
    """自动同步数据：检查market_data中的日期，下载缺失的daily_data"""
    logger.info("开始自动同步数据...")

    dates_to_sync = get_dates_to_sync()

    if not dates_to_sync:
        logger.info("所有日期的数据都已同步，无需下载")
        return True

    logger.info(f"发现 {len(dates_to_sync)} 个日期需要同步数据: {dates_to_sync}")

    success_count = 0
    total_count = len(dates_to_sync)

    for i, date_str in enumerate(dates_to_sync):
        logger.info(f"[{i+1}/{total_count}] 正在同步 {date_str} 的数据...")

        # 获取数据
        stock_data = get_stock_data(date_str)
        if stock_data:
            # 存储数据
            if save_data_to_db(stock_data, date_str):
                success_count += 1
                logger.info(f"✅ {date_str} 数据同步成功")
            else:
                logger.error(f"❌ {date_str} 数据保存失败")
        else:
            logger.warning(f"⚠️ 未获取到 {date_str} 的数据")

        # 每次请求后增加随机延迟，避免被封IP
        if i < total_count - 1:  # 最后一次不需要延迟
            delay = random.uniform(Config.MIN_DELAY, Config.MAX_DELAY)
            logger.info(f"等待 {delay:.2f} 秒后继续...")
            time.sleep(delay)

    logger.info(f"自动同步完成，成功同步 {success_count}/{total_count} 天的数据")
    return success_count == total_count

# 主函数
def main():
    """
    主函数：自动同步stock_market_data中的日期数据到stock_daily_data
    每天执行一遍，检查是否有缺失的数据需要下载
    """
    logger.info("=" * 60)
    logger.info("股票数据自动同步程序启动")
    logger.info("=" * 60)

    # 创建表
    create_table()

    try:
        # 执行自动同步
        success = auto_sync_data()

        if success:
            logger.info("🎉 所有数据同步完成！")
        else:
            logger.warning("⚠️ 部分数据同步失败，请检查日志")

    except Exception as e:
        logger.error(f"程序执行异常: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

    logger.info("程序执行完成！")

def main_interactive():
    """
    交互式主函数：保留原有的手动选择功能，用于测试和手动操作
    """
    create_table()

    # 获取最新交易日期
    latest_trading_date = get_latest_trading_date()
    print(f"数据库中最新交易日期: {latest_trading_date}")

    # 可以选择不同模式
    print("\n选择模式：")
    print("1 - 自动同步模式（推荐）")
    print("2 - 单日测试")
    print("3 - 批量获取最近几天数据")
    print("4 - 指定日期范围")

    mode = input("请选择模式 (默认1): ").strip()

    if mode == "1" or mode == "":
        # 自动同步模式
        print("开始自动同步...")
        success = auto_sync_data()
        if success:
            print("🎉 所有数据同步完成！")
        else:
            print("⚠️ 部分数据同步失败，请检查日志")

    elif mode == "2":
        # 单日测试模式
        date_str = input("输入要测试的日期 (YYYY-MM-DD，默认2025-07-28): ").strip()
        if not date_str:
            date_str = '2025-07-28'

        print(f"开始测试日期: {date_str}")
        print(f"正在获取 {date_str} 的数据...")

        # 获取数据
        stock_data = get_stock_data(date_str)
        if stock_data:
            # 存储数据
            save_data_to_db(stock_data, date_str)
            print(f"成功获取并保存 {date_str} 的数据")
        else:
            print(f"未获取到 {date_str} 的数据")

    elif mode == "3":
        # 批量获取最近几天的数据
        days = int(input("获取最近几天的数据 (默认5天): ") or "5")
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)

        trading_days = get_trading_days_in_range(start_date, end_date)
        print(f"将获取 {len(trading_days)} 个交易日的数据")

        success_count = 0
        for i, trade_date in enumerate(trading_days):
            date_str = trade_date.strftime('%Y-%m-%d')
            print(f"\n[{i+1}/{len(trading_days)}] 正在获取 {date_str} 的数据...")

            # 获取数据
            stock_data = get_stock_data(date_str)
            if stock_data:
                # 存储数据
                save_data_to_db(stock_data, date_str)
                success_count += 1
            else:
                print(f"未获取到 {date_str} 的数据")

            # 每次请求后增加随机延迟，避免被封IP
            if i < len(trading_days) - 1:  # 最后一次不需要延迟
                delay = random.uniform(Config.MIN_DELAY, Config.MAX_DELAY)
                logger.info(f"等待 {delay:.2f} 秒后继续...")
                time.sleep(delay)

        print(f"\n批量获取完成，成功获取 {success_count}/{len(trading_days)} 天的数据")

    elif mode == "4":
        # 指定日期范围
        start_str = input("输入开始日期 (YYYY-MM-DD): ").strip()
        end_str = input("输入结束日期 (YYYY-MM-DD): ").strip()

        try:
            start_date = datetime.strptime(start_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_str, '%Y-%m-%d').date()

            trading_days = get_trading_days_in_range(start_date, end_date)
            print(f"将获取 {len(trading_days)} 个交易日的数据")

            success_count = 0
            for i, trade_date in enumerate(trading_days):
                date_str = trade_date.strftime('%Y-%m-%d')
                print(f"\n[{i+1}/{len(trading_days)}] 正在获取 {date_str} 的数据...")

                # 获取数据
                stock_data = get_stock_data(date_str)
                if stock_data:
                    # 存储数据
                    save_data_to_db(stock_data, date_str)
                    success_count += 1
                else:
                    print(f"未获取到 {date_str} 的数据")

                # 每次请求后增加随机延迟
                if i < len(trading_days) - 1:
                    delay = random.uniform(Config.MIN_DELAY * 1.5, Config.MAX_DELAY * 1.5)  # 更长的延迟
                    logger.info(f"等待 {delay:.2f} 秒后继续...")
                    time.sleep(delay)

            print(f"\n批量获取完成，成功获取 {success_count}/{len(trading_days)} 天的数据")

        except ValueError:
            print("日期格式错误，请使用 YYYY-MM-DD 格式")
            return

    print("\n程序执行完成！")

if __name__ == '__main__':
    main()