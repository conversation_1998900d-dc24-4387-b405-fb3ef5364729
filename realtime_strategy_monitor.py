#!/usr/bin/env python3
"""
实时策略监控
查询最近15个交易日的策略结果股票的实时行情
"""

import time
import requests
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine, text
from config import Config
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('realtime_strategy_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealtimeStrategyMonitor:
    """实时策略监控器"""
    
    def __init__(self):
        self.engine = create_engine(Config.DATABASE_URL)
    
    def get_tencent_price(self, code):
        """获取腾讯实时行情数据"""
        retry_count = 0
        max_retries = 3
        while retry_count < max_retries:
            try:
                stock_list = []
                URL = f"https://web.sqt.gtimg.cn/utf8/q={code}&offset=1,2,3,4,5,6,7,39,31,34,35,33,46,38,32"
                st = requests.get(URL).content
                a = (
                    st.decode("utf-8")
                    .replace('="1', "")
                    .replace('";', "~")
                    .replace("\n", "")
                    .split("~")
                )
                for x in range(0, len(a) - 1, 15):
                    stock_list.append(
                        {
                            "stock_code": a[x][4:10],
                            "stock_name": a[x + 1],
                            "close": a[x + 3],
                            "pre_close": a[x + 4],
                            "open": a[x + 5],
                            "volume": float(a[x + 6]) * 100,
                            "turnover_ratio": a[x + 7],
                            "now_time": a[x + 8],
                            "high": a[x + 9],
                            "low": a[x + 10],
                            "change_pct": a[x + 11],
                            "sz": a[x + 12],
                            "amount": float(a[x + 13]) * 10000,
                            "change": a[x + 14]
                        }
                    )
                return stock_list

            except Exception as e:
                retry_count += 1
                if retry_count == max_retries:
                    raise Exception(f"获取腾讯行情数据失败，已重试{max_retries}次: {str(e)}")
                logger.warning(f"获取数据失败，10秒后进行第{retry_count + 1}次重试...")
                time.sleep(10)
    
    def get_recent_strategy_stocks(self, days=15):
        """获取最近N个交易日的策略结果股票"""
        try:
            with self.engine.connect() as conn:
                # 计算N个交易日前的日期（大概估算）
                start_date = datetime.now() - timedelta(days=days + 10)  # 多加几天确保覆盖
                
                query = text("""
                    SELECT DISTINCT stock_code, stock_name, concepts, entry_date
                    FROM new_strategy_results 
                    WHERE entry_date >= :start_date
                    ORDER BY entry_date DESC
                """)
                
                result = conn.execute(query, {'start_date': start_date.date()})
                rows = result.fetchall()
                
                if not rows:
                    logger.warning("没有找到最近的策略结果")
                    return []
                
                # 转换为字典列表
                stocks = []
                for row in rows:
                    stocks.append({
                        'stock_code': row[0],
                        'stock_name': row[1],
                        'concepts': row[2],
                        'entry_date': row[3]
                    })
                
                logger.info(f"获取到 {len(stocks)} 个最近的策略股票")
                return stocks
                
        except Exception as e:
            logger.error(f"获取策略股票失败: {str(e)}")
            return []
    
    def format_stock_codes(self, stocks):
        """格式化股票代码，添加市场前缀"""
        formatted_codes = []
        
        for stock in stocks:
            code = stock['stock_code']
            
            # 判断市场并添加前缀
            if code.startswith('00') or code.startswith('30'):
                # 深市：000xxx, 002xxx, 300xxx
                formatted_code = f"sz{code}"
            elif code.startswith('60') or code.startswith('68'):
                # 沪市：600xxx, 601xxx, 603xxx, 605xxx, 688xxx
                formatted_code = f"sh{code}"
            else:
                # 其他情况，默认深市
                formatted_code = f"sz{code}"
            
            formatted_codes.append(formatted_code)
        
        return formatted_codes
    
    def batch_get_realtime_data(self, stocks):
        """批量获取实时行情数据"""
        try:
            # 格式化股票代码
            formatted_codes = self.format_stock_codes(stocks)
            
            # 分批处理，每批最多500个
            batch_size = 500
            all_realtime_data = []
            
            for i in range(0, len(formatted_codes), batch_size):
                batch_codes = formatted_codes[i:i + batch_size]
                batch_stocks = stocks[i:i + batch_size]
                
                # 构建查询字符串
                code_string = ','.join(batch_codes)
                
                logger.info(f"获取第 {i//batch_size + 1} 批实时数据，共 {len(batch_codes)} 个股票")
                
                # 获取实时数据
                realtime_data = self.get_tencent_price(code_string)
                
                # 合并策略信息和实时数据
                for j, rt_data in enumerate(realtime_data):
                    if j < len(batch_stocks):
                        strategy_info = batch_stocks[j]
                        
                        # 合并数据
                        combined_data = {
                            'stock_code': strategy_info['stock_code'],
                            'stock_name': strategy_info['stock_name'],
                            'concepts': strategy_info['concepts'],
                            'entry_date': strategy_info['entry_date'],
                            'current_price': float(rt_data['close']) if rt_data['close'] else 0,
                            'change_pct': float(rt_data['change_pct']) if rt_data['change_pct'] else 0,
                            'change': float(rt_data['change']) if rt_data['change'] else 0,
                            'volume': rt_data['volume'],
                            'turnover_ratio': float(rt_data['turnover_ratio']) if rt_data['turnover_ratio'] else 0,
                            'high': float(rt_data['high']) if rt_data['high'] else 0,
                            'low': float(rt_data['low']) if rt_data['low'] else 0,
                            'open': float(rt_data['open']) if rt_data['open'] else 0,
                            'now_time': rt_data['now_time']
                        }
                        
                        all_realtime_data.append(combined_data)
                
                # 避免请求过于频繁
                if i + batch_size < len(formatted_codes):
                    time.sleep(1)
            
            return all_realtime_data
            
        except Exception as e:
            logger.error(f"批量获取实时数据失败: {str(e)}")
            return []
    
    def display_results(self, data):
        """显示结果"""
        if not data:
            print("❌ 没有获取到实时数据")
            return

        # 按当日涨幅从高到低排序
        sorted_data = sorted(data, key=lambda x: x['change_pct'], reverse=True)

        print("\n" + "=" * 150)
        print("📊 最近15个交易日策略股票实时行情监控")
        print("=" * 150)
        print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<10} {'当前价格':<8} {'涨跌幅%':<12} {'涨跌额':<8} {'成交量(万)':<10} {'入选时间':<12} {'概念':<40}")
        print("-" * 150)

        for i, stock in enumerate(sorted_data, 1):
            # 涨跌幅颜色标识
            change_pct = stock['change_pct']
            if change_pct > 0:
                change_color = f"🔴+{change_pct:.2f}%"
            elif change_pct < 0:
                change_color = f"🟢{change_pct:.2f}%"
            else:
                change_color = f"⚪{change_pct:.2f}%"

            change_amount = stock['change']
            if change_amount > 0:
                change_amount_str = f"+{change_amount:.2f}"
            else:
                change_amount_str = f"{change_amount:.2f}"

            # 成交量转换为万手
            volume_wan = stock['volume'] / 10000 if stock['volume'] else 0

            # 截断概念字符串
            concepts = stock['concepts'][:35] + "..." if len(stock['concepts']) > 35 else stock['concepts']

            # 格式化入选时间
            entry_date_str = str(stock['entry_date']) if stock['entry_date'] else "未知"

            print(f"{i:<4} {stock['stock_code']:<8} {stock['stock_name']:<10} {stock['current_price']:<8.2f} "
                  f"{change_color:<15} {change_amount_str:<8} {volume_wan:<10.0f} {entry_date_str:<12} {concepts:<40}")

        print("-" * 150)

        # 统计信息
        positive_count = len([x for x in sorted_data if x['change_pct'] > 0])
        negative_count = len([x for x in sorted_data if x['change_pct'] < 0])
        flat_count = len([x for x in sorted_data if x['change_pct'] == 0])

        print(f"📈 总计: {len(sorted_data)} 个股票")
        print(f"🔴 上涨: {positive_count} 个 ({positive_count/len(sorted_data)*100:.1f}%)")
        print(f"🟢 下跌: {negative_count} 个 ({negative_count/len(sorted_data)*100:.1f}%)")
        print(f"⚪ 平盘: {flat_count} 个 ({flat_count/len(sorted_data)*100:.1f}%)")

        if sorted_data:
            avg_change = sum(x['change_pct'] for x in sorted_data) / len(sorted_data)
            max_gain = max(x['change_pct'] for x in sorted_data)
            max_loss = min(x['change_pct'] for x in sorted_data)

            print(f"📊 平均涨跌幅: {avg_change:.2f}%")
            print(f"🚀 最大涨幅: {max_gain:.2f}% ({sorted_data[0]['stock_code']} - {sorted_data[0]['stock_name']})")
            print(f"📉 最大跌幅: {max_loss:.2f}% ({sorted_data[-1]['stock_code']} - {sorted_data[-1]['stock_name']})")

        print("=" * 150)

        # 发送前20名到钉钉
        self.send_top_stocks_to_dingtalk(sorted_data[:20])

    def send_top_stocks_to_dingtalk(self, top_stocks):
        """发送前20名股票到钉钉"""
        try:
            if not top_stocks:
                return

            # 钉钉Webhook URL
            webhook_url = "https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55"

            # 构建消息内容
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            message_content = f"## 📊 策略股票实时监控 TOP20\n\n"
            message_content += f"**监控时间**: {current_time}\n\n"

            for i, stock in enumerate(top_stocks, 1):
                # 格式化涨跌幅
                change_pct = stock['change_pct']
                if change_pct > 0:
                    change_str = f"+{change_pct:.2f}%"
                elif change_pct < 0:
                    change_str = f"{change_pct:.2f}%"
                else:
                    change_str = "0.00%"

                # 截断概念字符串
                concepts = stock['concepts'][:20] + "..." if len(stock['concepts']) > 20 else stock['concepts']

                message_content += f"**{i}. {stock['stock_code']} - {stock['stock_name']}**\n"
                message_content += f"- 价格: {stock['current_price']:.2f}元\n"
                message_content += f"- 涨幅: {change_str}\n"
                message_content += f"- 入选: {stock['entry_date']}\n"
                message_content += f"- 概念: {concepts}\n\n"

            # 添加统计信息
            positive_count = len([x for x in top_stocks if x['change_pct'] > 0])
            avg_change = sum(x['change_pct'] for x in top_stocks) / len(top_stocks)

            message_content += f"**📈 TOP20统计**\n"
            message_content += f"- 上涨: {positive_count}/{len(top_stocks)} 个\n"
            message_content += f"- 平均涨幅: {avg_change:.2f}%\n"

            # 发送钉钉消息
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": "策略股票实时监控 TOP20",
                    "text": message_content
                }
            }

            response = requests.post(
                webhook_url,
                json=message,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("📱 TOP20结果已发送到钉钉")
                    logger.info("TOP20结果已发送到钉钉")
                else:
                    print(f"⚠️ 钉钉消息发送失败: {result}")
                    logger.warning(f"钉钉消息发送失败: {result}")
            else:
                print(f"⚠️ 钉钉请求失败: {response.status_code}")
                logger.warning(f"钉钉请求失败: {response.status_code}")

        except Exception as e:
            logger.warning(f"发送钉钉消息异常: {str(e)}")
            print(f"⚠️ 发送钉钉消息失败: {str(e)}")

    def run_monitor(self, days=15):
        """运行监控"""
        try:
            print("🚀 启动实时策略监控")
            print(f"⏰ 监控时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📅 监控范围: 最近{days}个交易日")

            # 获取最近N个交易日的策略股票
            logger.info(f"获取最近{days}个交易日的策略股票...")
            stocks = self.get_recent_strategy_stocks(days=days)
            
            if not stocks:
                print("❌ 没有找到最近的策略股票")
                return
            
            print(f"📋 找到 {len(stocks)} 个策略股票")
            
            # 获取实时行情数据
            logger.info("获取实时行情数据...")
            realtime_data = self.batch_get_realtime_data(stocks)
            
            if not realtime_data:
                print("❌ 获取实时数据失败")
                return
            
            # 显示结果
            self.display_results(realtime_data)
            
            logger.info("实时监控完成")
            
        except Exception as e:
            logger.error(f"运行监控失败: {str(e)}")
            print(f"❌ 监控失败: {str(e)}")

def main():
    """主函数"""
    import sys

    try:
        # 解析命令行参数
        days = 15  # 默认15天
        if len(sys.argv) > 1:
            try:
                days = int(sys.argv[1])
                if days <= 0:
                    print("❌ 天数必须大于0")
                    return
            except ValueError:
                print("❌ 请输入有效的天数")
                return

        monitor = RealtimeStrategyMonitor()
        monitor.run_monitor(days=days)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断监控")
    except Exception as e:
        print(f"❌ 程序异常: {str(e)}")

if __name__ == '__main__':
    main()
