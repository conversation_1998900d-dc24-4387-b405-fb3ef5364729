#!/usr/bin/env python3
"""
使用代理池同步股票数据的示例脚本
"""

import logging
from sync_kline_by_concepts_chgip import ConceptBasedKlineSync

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_with_proxy():
    """演示使用代理池同步数据"""
    print("🚀 开始演示 - 使用代理池同步股票数据")
    print("=" * 60)
    
    # 创建同步器（默认使用代理）
    syncer = ConceptBasedKlineSync(
        top_count=5,  # 只取前5个概念进行演示
        lookback_months=1,  # 只回看1个月数据进行演示
        use_proxy=True
    )
    
    try:
        # 运行同步
        syncer.run()
        print("\n✅ 代理池同步演示完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断演示")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {str(e)}")

def demo_without_proxy():
    """演示不使用代理同步数据"""
    print("🚀 开始演示 - 不使用代理同步股票数据")
    print("=" * 60)
    
    # 创建同步器（不使用代理）
    syncer = ConceptBasedKlineSync(
        top_count=3,  # 只取前3个概念进行演示
        lookback_months=1,  # 只回看1个月数据进行演示
        use_proxy=False
    )
    
    try:
        # 只分析概念，不同步数据
        print("🔍 只分析热门概念...")
        hot_concepts = syncer.analyze_hot_concepts()
        
        if hot_concepts:
            print(f"\n📊 发现 {len(hot_concepts)} 个热门概念")
            stock_codes = syncer.get_stocks_by_concepts(hot_concepts)
            print(f"📈 相关股票数量: {len(stock_codes)}")
        
        print("\n✅ 无代理演示完成")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {str(e)}")

if __name__ == '__main__':
    print("🎯 股票数据同步代理池演示")
    print("=" * 60)
    print("1. 使用代理池同步数据")
    print("2. 不使用代理分析概念")
    print("3. 退出")
    print("=" * 60)
    
    choice = input("请选择演示模式 (1/2/3): ").strip()
    
    if choice == '1':
        demo_with_proxy()
    elif choice == '2':
        demo_without_proxy()
    elif choice == '3':
        print("👋 感谢使用，再见！")
    else:
        print("❌ 无效选择，退出演示")