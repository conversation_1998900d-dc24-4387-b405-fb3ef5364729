# 股票数据同步代理池使用说明

## 功能介绍

为了解决IP被封的问题，`sync_kline_by_concepts_chgip.py` 现在支持自动切换IP代理池来获取股票数据。系统会自动从免费代理源获取代理IP，并在请求失败时自动切换到下一个可用代理。

## 主要特性

- 🌐 **自动代理获取**: 从多个免费代理源自动获取代理IP
- 🔄 **智能切换**: 代理失败时自动切换到下一个可用代理
- 📊 **代理池管理**: 自动维护代理池，移除失效代理
- ⚡ **备用代理**: 当API不可用时使用预设的备用代理列表
- 🛡️ **容错机制**: 多重重试和错误处理机制

## 使用方法

### 1. 默认使用代理池（推荐）
```bash
python3 sync_kline_by_concepts_chgip.py
```

### 2. 不使用代理（直连模式）
```bash
python3 sync_kline_by_concepts_chgip.py --no-proxy
```

### 3. 只分析概念（不使用代理）
```bash
python3 sync_kline_by_concepts_chgip.py --analyze-only
```

### 4. 查看帮助
```bash
python3 sync_kline_by_concepts_chgip.py --help
```

## 代码集成

### 在代码中使用代理池

```python
from sync_kline_by_concepts_chgip import ConceptBasedKlineSync

# 使用代理池（默认）
syncer = ConceptBasedKlineSync(use_proxy=True)
syncer.run()

# 不使用代理
syncer = ConceptBasedKlineSync(use_proxy=False)
syncer.run()
```

### 自定义代理配置

```python
# 自定义参数
syncer = ConceptBasedKlineSync(
    top_count=18,              # 取前N个热门概念
    lookback_months=6,         # 回看几个月的数据
    use_proxy=True,            # 是否使用代理池
    blocked_concepts=['概念1', '概念2']  # 屏蔽的概念
)
```

## 代理池工作原理

1. **代理获取**: 系统启动时自动从免费代理API获取代理列表
2. **代理验证**: 自动验证代理格式和可用性
3. **智能切换**: 当某个代理失败时，自动标记为失败并切换到下一个代理
4. **池维护**: 定期更新代理池，移除长期失效的代理
5. **备用机制**: 当API不可用时，使用预设的备用代理列表

## 日志信息

系统会输出详细的代理使用日志：

```
🌐 正在初始化代理池...
✅ 代理池初始化完成，可用代理数: 15
🌐 使用代理 http://xxx.xxx.xxx.xxx:xxxx 获取股票 000001 数据...
✅ 代理 http://xxx.xxx.xxx.xxx:xxxx 成功获取股票 000001 数据，数据量: 120
🔄 切换到代理 2/15
❌ 代理 http://xxx.xxx.xxx.xxx:xxxx 已标记为失败并从池中移除
```

## 故障排除

### 代理获取失败
- 检查网络连接
- 确认防火墙没有阻止访问代理API
- 系统会自动使用备用代理列表

### 所有代理都失败
- 系统会自动回退到直连模式
- 可以使用 `--no-proxy` 参数强制使用直连模式

### 代理速度太慢
- 系统会自动切换到下一个代理
- 可以在代码中调整超时时间参数

## 注意事项

1. **免费代理限制**: 免费代理可能存在稳定性和速度问题
2. **使用频率**: 建议合理设置请求间隔，避免过于频繁的请求
3. **备用方案**: 始终保留直连模式作为最后的选择
4. **代理质量**: 免费代理的质量参差不齐，可能需要多次尝试才能找到稳定的代理

## 性能优化建议

1. **调整重试次数**: 根据网络环境调整 `MAX_RETRIES` 参数
2. **优化延迟时间**: 调整 `MIN_DELAY` 和 `MAX_DELAY` 参数
3. **代理池大小**: 根据需求调整代理池的最小大小
4. **定期更新**: 代理池会自动定期更新，确保代理的可用性

## 演示脚本

运行演示脚本来测试代理功能：

```bash
python3 sync_with_proxy_demo.py
```

选择相应的选项来体验不同的工作模式。