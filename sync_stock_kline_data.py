#!/usr/bin/env python3
"""
股票日K数据同步和技术指标计算脚本
功能：
1. 从stock_daily_data表获取最近4个月的所有股票代码
2. 通过adata库获取这些股票的日K数据
3. 计算技术指标
4. 保存到数据库
"""

import pandas as pd
import numpy as np
import adata
import logging
import time
import random
from datetime import datetime, timedelta
from sqlalchemy import create_engine, Column, String, Integer, Float, Date, text
from sqlalchemy.orm import declarative_base, sessionmaker
from sqlalchemy import inspect
from config import Config

# 配置日志
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format=Config.LOG_FORMAT,
    handlers=[
        logging.FileHandler('stock_kline_sync.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库连接
engine = create_engine(Config.DATABASE_URL)
Base = declarative_base()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 定义股票K线数据模型
class StockKlineData(Base):
    __tablename__ = 'stock_kline_data'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    stock_code = Column(String(20), nullable=False, comment='股票代码')
    trade_date = Column(Date, nullable=False, comment='交易日期')
    open_price = Column(Float, comment='开盘价')
    high_price = Column(Float, comment='最高价')
    low_price = Column(Float, comment='最低价')
    close_price = Column(Float, comment='收盘价')
    volume = Column(Float, comment='成交量')
    amount = Column(Float, comment='成交额')
    turnover_rate = Column(Float, comment='换手率')
    
    # 技术指标字段
    ma5 = Column(Float, comment='5日均线')
    ma10 = Column(Float, comment='10日均线')
    ma20 = Column(Float, comment='20日均线')
    
    # 最近3个月统计指标
    high_3m = Column(Float, comment='最近3个月最高价')
    low_3m = Column(Float, comment='最近3个月最低价')
    max_turnover_3m = Column(Float, comment='最近3个月最大换手率')
    min_turnover_3m = Column(Float, comment='最近3个月最小换手率')
    max_volume_3m = Column(Float, comment='最近3个月最大成交量')
    min_volume_3m = Column(Float, comment='最近3个月最小成交量')
    limit_up_count_3m = Column(Integer, comment='最近3个月涨停次数')
    limit_down_count_3m = Column(Integer, comment='最近3个月跌停次数')
    
    # 最近1个月统计指标
    limit_up_count_1m = Column(Integer, comment='最近1个月涨停次数')
    limit_down_count_1m = Column(Integer, comment='最近1个月跌停次数')

    # 新增统计指标
    avg_volume_3m = Column(Float, comment='最近3个月平均成交量')
    avg_amount_3m = Column(Float, comment='最近3个月平均成交额')

def create_kline_table():
    """创建K线数据表并更新表结构"""
    inspector = inspect(engine)
    if not inspector.has_table('stock_kline_data'):
        Base.metadata.create_all(bind=engine)
        logger.info("表 stock_kline_data 创建成功")
    else:
        logger.info("表 stock_kline_data 已存在，检查表结构...")

        # 检查并添加新字段
        try:
            # 获取现有列名
            columns = inspector.get_columns('stock_kline_data')
            column_names = [col['name'] for col in columns]

            # 需要添加的新字段
            new_fields = [
                ('avg_volume_3m', 'FLOAT', '最近3个月平均成交量'),
                ('avg_amount_3m', 'FLOAT', '最近3个月平均成交额')
            ]

            # 检查并添加缺失的字段
            with engine.connect() as conn:
                for field_name, field_type, field_comment in new_fields:
                    if field_name not in column_names:
                        logger.info(f"添加新字段: {field_name}")
                        sql = f"ALTER TABLE stock_kline_data ADD COLUMN {field_name} {field_type} COMMENT '{field_comment}'"
                        conn.execute(text(sql))
                        conn.commit()
                        logger.info(f"✅ 字段 {field_name} 添加成功")
                    else:
                        logger.debug(f"字段 {field_name} 已存在")

            logger.info("表结构检查完成")

        except Exception as e:
            logger.error(f"表结构更新失败: {str(e)}")
            # 如果更新失败，尝试重新创建表
            logger.info("尝试重新创建表...")
            Base.metadata.create_all(bind=engine)
            logger.info("表重新创建完成")

def get_stock_codes_from_daily_data():
    """从stock_daily_data表获取最近4个月的所有股票代码（去重）"""
    logger.info("开始获取最近4个月的股票代码...")
    
    # 计算4个月前的日期
    four_months_ago = datetime.now() - timedelta(days=240)
    start_date = four_months_ago.strftime('%Y-%m-%d')
    
    session = SessionLocal()
    try:
        # 查询最近4个月的股票代码并去重
        query = text("""
            SELECT DISTINCT stock_code 
            FROM stock_daily_data 
            WHERE trade_day >= :start_date 
            ORDER BY stock_code
        """)
        
        result = session.execute(query, {'start_date': start_date})
        stock_codes = [row[0] for row in result.fetchall()]
        
        logger.info(f"获取到 {len(stock_codes)} 个股票代码，时间范围：{start_date} 至今")
        return stock_codes, start_date
        
    except Exception as e:
        logger.error(f"获取股票代码失败: {str(e)}")
        return [], start_date
    finally:
        session.close()

def get_stock_kline_data_with_retry(stock_code, start_date, max_retries=3, retry_delay=30):
    """获取股票K线数据 - 带重试机制和随机延迟"""

    for attempt in range(max_retries + 1):  # 总共尝试 max_retries + 1 次
        try:
            if attempt > 0:
                logger.info(f"🔄 正在重试获取股票 {stock_code} 的K线数据... (第{attempt}/{max_retries}次重试)")
            else:
                logger.info(f"📡 正在获取股票 {stock_code} 的K线数据...")

            # 使用adata获取K线数据
            df = adata.stock.market.get_market(
                stock_code=stock_code,
                k_type=1,  # 日K线
                start_date=start_date
            )

            if df is not None and not df.empty:
                logger.info(f"✅ 股票 {stock_code} 获取到 {len(df)} 条K线数据")

                # 成功获取数据后，随机暂停1-3秒
                random_delay = random.uniform(5, 10)
                logger.debug(f"⏱️ 随机延迟 {random_delay:.1f} 秒避免请求过频...")
                time.sleep(random_delay)

                return df
            else:
                logger.warning(f"⚠️ 股票 {stock_code} 未获取到K线数据")

                # 如果还有重试机会，等待后重试
                if attempt < max_retries:
                    logger.info(f"⏳ 等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"❌ 股票 {stock_code} 重试 {max_retries} 次后仍无法获取数据")
                    return None

        except Exception as e:
            logger.error(f"❌ 获取股票 {stock_code} K线数据异常: {str(e)}")

            # 如果还有重试机会，等待后重试
            if attempt < max_retries:
                logger.info(f"⏳ 异常重试，等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                continue
            else:
                logger.error(f"❌ 股票 {stock_code} 重试 {max_retries} 次后仍然失败")
                return None

    return None

def get_stock_kline_data(stock_code, start_date):
    """获取单个股票的K线数据 - 兼容性包装函数"""
    return get_stock_kline_data_with_retry(stock_code, start_date)

def calculate_technical_indicators(df, stock_code):
    """计算技术指标 - 使用rolling函数计算滑动窗口指标"""
    if df is None or df.empty:
        return df

    try:
        # 确保数据按日期排序
        df = df.sort_values('trade_date')
        df = df.reset_index(drop=True)

        # 计算移动平均线
        df['ma5'] = df['close'].rolling(window=5, min_periods=1).mean()
        df['ma10'] = df['close'].rolling(window=10, min_periods=1).mean()
        df['ma20'] = df['close'].rolling(window=20, min_periods=1).mean()

        # 计算涨停和跌停（假设涨跌幅超过9.8%为涨跌停）
        df['pct_change'] = df['close'].pct_change() * 100
        df['is_limit_up'] = (df['pct_change'] >= 9.8).astype(int)
        df['is_limit_down'] = (df['pct_change'] <= -9.8).astype(int)

        # 使用rolling函数计算最近3个月的统计指标（90个交易日）
        # 注意：这里使用固定的90天窗口，而不是min(90, len(df))，确保rolling计算的准确性
        window_3m = 90
        df['high_3m'] = df['high'].rolling(window=window_3m, min_periods=1).max()
        df['low_3m'] = df['low'].rolling(window=window_3m, min_periods=1).min()

        # 处理换手率字段（adata返回的字段名可能是turnover_ratio）
        turnover_col = None
        if 'turnover_ratio' in df.columns:
            turnover_col = 'turnover_ratio'
        elif 'turnover_rate' in df.columns:
            turnover_col = 'turnover_rate'

        if turnover_col:
            df['max_turnover_3m'] = df[turnover_col].rolling(window=window_3m, min_periods=1).max()
            df['min_turnover_3m'] = df[turnover_col].rolling(window=window_3m, min_periods=1).min()
        else:
            df['max_turnover_3m'] = np.nan
            df['min_turnover_3m'] = np.nan

        # 使用rolling计算成交量统计
        df['max_volume_3m'] = df['volume'].rolling(window=window_3m, min_periods=1).max()
        df['min_volume_3m'] = df['volume'].rolling(window=window_3m, min_periods=1).min()

        # 使用rolling计算涨跌停次数
        df['limit_up_count_3m'] = df['is_limit_up'].rolling(window=window_3m, min_periods=1).sum()
        df['limit_down_count_3m'] = df['is_limit_down'].rolling(window=window_3m, min_periods=1).sum()

        # 使用rolling函数计算最近1个月的统计指标（30个交易日）
        window_1m = 30
        df['limit_up_count_1m'] = df['is_limit_up'].rolling(window=window_1m, min_periods=1).sum()
        df['limit_down_count_1m'] = df['is_limit_down'].rolling(window=window_1m, min_periods=1).sum()

        # 添加更多rolling统计指标
        df['avg_volume_3m'] = df['volume'].rolling(window=window_3m, min_periods=1).mean()
        df['avg_amount_3m'] = df['amount'].rolling(window=window_3m, min_periods=1).mean() if 'amount' in df.columns else np.nan

        logger.info(f"股票 {stock_code} 技术指标计算完成，共计算 {len([col for col in df.columns if col.endswith(('_3m', '_1m', 'ma5', 'ma10', 'ma20'))])} 个指标")
        return df

    except Exception as e:
        logger.error(f"计算股票 {stock_code} 技术指标失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return df

def save_kline_data_to_db(df, stock_code):
    """保存K线数据到数据库 - 优化批量处理"""
    if df is None or df.empty:
        logger.warning(f"股票 {stock_code} 数据为空，跳过保存")
        return False

    session = SessionLocal()
    try:
        # 先删除该股票的现有数据
        deleted_count = session.query(StockKlineData).filter(StockKlineData.stock_code == stock_code).delete()
        if deleted_count > 0:
            logger.info(f"删除股票 {stock_code} 的 {deleted_count} 条旧数据")

        # 准备批量插入的数据
        kline_records = []
        failed_rows = 0

        for idx, row in df.iterrows():
            try:
                # 处理日期格式
                if isinstance(row['trade_date'], str):
                    trade_date = datetime.strptime(row['trade_date'], '%Y-%m-%d').date()
                else:
                    trade_date = row['trade_date']

                # 处理换手率字段名
                turnover_value = None
                if 'turnover_ratio' in row and pd.notna(row['turnover_ratio']):
                    turnover_value = float(row['turnover_ratio'])
                elif 'turnover_rate' in row and pd.notna(row['turnover_rate']):
                    turnover_value = float(row['turnover_rate'])

                kline_data = StockKlineData(
                    stock_code=stock_code,
                    trade_date=trade_date,
                    open_price=float(row['open']) if pd.notna(row['open']) else None,
                    high_price=float(row['high']) if pd.notna(row['high']) else None,
                    low_price=float(row['low']) if pd.notna(row['low']) else None,
                    close_price=float(row['close']) if pd.notna(row['close']) else None,
                    volume=float(row['volume']) if pd.notna(row['volume']) else None,
                    amount=float(row['amount']) if 'amount' in row and pd.notna(row['amount']) else None,
                    turnover_rate=turnover_value,

                    # 技术指标
                    ma5=float(row['ma5']) if pd.notna(row['ma5']) else None,
                    ma10=float(row['ma10']) if pd.notna(row['ma10']) else None,
                    ma20=float(row['ma20']) if pd.notna(row['ma20']) else None,

                    # 3个月统计指标
                    high_3m=float(row['high_3m']) if pd.notna(row['high_3m']) else None,
                    low_3m=float(row['low_3m']) if pd.notna(row['low_3m']) else None,
                    max_turnover_3m=float(row['max_turnover_3m']) if pd.notna(row['max_turnover_3m']) else None,
                    min_turnover_3m=float(row['min_turnover_3m']) if pd.notna(row['min_turnover_3m']) else None,
                    max_volume_3m=float(row['max_volume_3m']) if pd.notna(row['max_volume_3m']) else None,
                    min_volume_3m=float(row['min_volume_3m']) if pd.notna(row['min_volume_3m']) else None,
                    limit_up_count_3m=int(row['limit_up_count_3m']) if pd.notna(row['limit_up_count_3m']) else None,
                    limit_down_count_3m=int(row['limit_down_count_3m']) if pd.notna(row['limit_down_count_3m']) else None,

                    # 1个月统计指标
                    limit_up_count_1m=int(row['limit_up_count_1m']) if pd.notna(row['limit_up_count_1m']) else None,
                    limit_down_count_1m=int(row['limit_down_count_1m']) if pd.notna(row['limit_down_count_1m']) else None,

                    # 新增统计指标
                    avg_volume_3m=float(row['avg_volume_3m']) if pd.notna(row['avg_volume_3m']) else None,
                    avg_amount_3m=float(row['avg_amount_3m']) if pd.notna(row['avg_amount_3m']) else None,
                )
                kline_records.append(kline_data)

            except Exception as row_error:
                failed_rows += 1
                logger.warning(f"股票 {stock_code} 第 {idx} 行数据处理失败: {str(row_error)}")
                continue

        if not kline_records:
            logger.error(f"股票 {stock_code} 没有有效的数据记录可以保存")
            return False

        # 使用批量插入优化性能
        session.add_all(kline_records)
        session.commit()

        success_msg = f"成功保存股票 {stock_code} 的 {len(kline_records)} 条K线数据"
        if failed_rows > 0:
            success_msg += f"（跳过 {failed_rows} 条无效数据）"
        logger.info(success_msg)
        return True

    except Exception as e:
        session.rollback()
        logger.error(f"保存股票 {stock_code} K线数据失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return False
    finally:
        session.close()

def batch_process_stocks(stock_codes, start_date, batch_size=50):
    """批量处理股票数据 - 进一步优化性能"""
    total_count = len(stock_codes)
    success_count = 0
    failed_count = 0
    skipped_count = 0

    for batch_start in range(0, total_count, batch_size):
        batch_end = min(batch_start + batch_size, total_count)
        batch_stocks = stock_codes[batch_start:batch_end]

        logger.info(f"🔄 开始处理批次 {batch_start//batch_size + 1}: 股票 {batch_start+1}-{batch_end}")

        batch_success = 0
        batch_failed = 0
        batch_skipped = 0

        for i, stock_code in enumerate(batch_stocks):
            global_index = batch_start + i + 1
            current_progress = f"[{global_index}/{total_count}]"

            try:
                # 获取K线数据
                df = get_stock_kline_data(stock_code, start_date)

                if df is not None and not df.empty:
                    # 计算技术指标
                    df = calculate_technical_indicators(df, stock_code)

                    # 保存到数据库
                    if save_kline_data_to_db(df, stock_code):
                        batch_success += 1
                        logger.info(f"✅ {current_progress} 股票 {stock_code} 处理成功")
                    else:
                        batch_failed += 1
                        logger.error(f"❌ {current_progress} 股票 {stock_code} 保存失败")
                else:
                    batch_skipped += 1
                    logger.warning(f"⚠️ {current_progress} 股票 {stock_code} 未获取到数据")

            except Exception as e:
                batch_failed += 1
                logger.error(f"❌ {current_progress} 处理股票 {stock_code} 异常: {str(e)}")

            # 注意：随机延迟已在get_stock_kline_data_with_retry中处理
            # 这里只在批次内每20个股票做一个小的进度报告
            if (i + 1) % 20 == 0 and i + 1 < len(batch_stocks):
                logger.info(f"📈 批次内进度: 已处理 {i+1}/{len(batch_stocks)} 个股票")

        success_count += batch_success
        failed_count += batch_failed
        skipped_count += batch_skipped

        logger.info(f"📊 批次 {batch_start//batch_size + 1} 完成: ✅{batch_success} ❌{batch_failed} ⚠️{batch_skipped}")

        # 批次间暂停
        if batch_end < total_count:
            logger.info("⏸️ 批次间暂停3秒...")
            time.sleep(3)

    return success_count, failed_count, skipped_count

def sync_all_stock_kline_data():
    """同步所有股票的K线数据 - 增强错误处理和请求控制"""
    logger.info("=" * 60)
    logger.info("开始同步股票K线数据")
    logger.info("=" * 60)

    # 创建表
    create_kline_table()

    # 获取股票代码列表
    stock_codes, start_date = get_stock_codes_from_daily_data()

    if not stock_codes:
        logger.error("未获取到股票代码，程序退出")
        return False

    total_count = len(stock_codes)
    logger.info(f"准备处理 {total_count} 个股票，时间范围：{start_date} 至今")

    # 记录开始时间
    start_time = time.time()

    # 使用批量处理优化性能
    success_count, failed_count, skipped_count = batch_process_stocks(stock_codes, start_date, batch_size=50)

    # 计算总耗时
    total_time = time.time() - start_time

    logger.info("=" * 60)
    logger.info("📈 K线数据同步完成！")
    logger.info(f"📊 处理统计:")
    logger.info(f"   📦 总股票数: {total_count}")
    logger.info(f"   ✅ 成功处理: {success_count}")
    logger.info(f"   ❌ 处理失败: {failed_count}")
    logger.info(f"   ⚠️ 跳过处理: {skipped_count}")
    logger.info(f"   📈 成功率: {(success_count/total_count)*100:.1f}%")
    logger.info(f"   ⏱️ 总耗时: {total_time/60:.1f}分钟")
    logger.info(f"   ⚡ 平均速度: {total_time/total_count:.2f}秒/股票")
    logger.info("=" * 60)

    # 返回处理结果
    return {
        'total': total_count,
        'success': success_count,
        'failed': failed_count,
        'skipped': skipped_count,
        'success_rate': (success_count/total_count)*100 if total_count > 0 else 0,
        'total_time': total_time
    }

def main():
    """主函数 - 增强错误处理"""
    try:
        logger.info("🚀 股票K线数据同步程序启动")
        result = sync_all_stock_kline_data()

        if result:
            if result['success_rate'] >= 80:
                logger.info("🎉 同步任务完成，成功率良好！")
            elif result['success_rate'] >= 50:
                logger.warning("⚠️ 同步任务完成，但成功率偏低，请检查网络或API状态")
            else:
                logger.error("❌ 同步任务完成，但成功率很低，建议检查配置和网络")
        else:
            logger.error("❌ 同步任务失败，未能获取股票代码")

    except KeyboardInterrupt:
        logger.warning("⚠️ 用户中断程序执行")
    except Exception as e:
        logger.error(f"❌ 程序执行异常: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
    finally:
        logger.info("📝 程序执行结束")

if __name__ == '__main__':
    main()
