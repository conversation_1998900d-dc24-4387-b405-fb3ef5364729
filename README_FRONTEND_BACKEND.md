# 股票TOP20监控系统 - 前后端分离版本

## 🎯 项目概述

这是一个基于FastAPI + Vue3的前后端分离股票监控系统，提供实时TOP20股票排行榜展示功能。

### 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端服务器     │    │   后端API服务    │    │   MySQL数据库    │
│  (Port: 3000)   │◄──►│  (Port: 8000)   │◄──►│                │
│   Vue3 + HTML   │    │   FastAPI       │    │  top20_cache   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速启动

### 1. 环境准备
```bash
# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install fastapi uvicorn passlib[bcrypt] python-jose python-multipart
```

### 2. 数据库初始化
```bash
# 创建数据库表
python3 init_web_database.py
```

### 3. 启动系统
```bash
# 一键启动前后端服务
./start_frontend_backend.sh

# 或者手动启动
# 后端API服务
python3 -m uvicorn backend_api:app --host 127.0.0.1 --port 8000 &

# 前端服务
python3 -m uvicorn frontend_server:app --host 127.0.0.1 --port 3000 &
```

### 4. 访问系统
- **前端界面**: http://127.0.0.1:3000
- **后端API文档**: http://127.0.0.1:8000/docs
- **API健康检查**: http://127.0.0.1:8000/api/health

## 👤 测试账号

| 用户类型 | 用户名 | 密码 | 权限 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 完整访问 |
| 普通用户 | user | user123 | 查看数据 |

## 📁 项目结构

```
├── backend_api.py              # 后端API服务
├── frontend_server.py          # 前端静态文件服务器
├── frontend/                   # 前端文件目录
│   ├── public/
│   │   ├── index.html         # 主页面
│   │   └── test.html          # 测试页面
│   └── src/
│       ├── app_fixed.js       # 主应用逻辑
│       └── simple_app.js      # 简化测试应用
├── init_web_database.py        # 数据库初始化
├── start_frontend_backend.sh   # 启动脚本
├── stop_frontend_backend.sh    # 停止脚本
└── task_scheduler.py           # 任务调度器(数据更新)
```

## 🔧 技术栈

### 后端技术
- **FastAPI**: 高性能异步Web框架
- **SQLAlchemy**: ORM数据库操作
- **JWT**: 用户认证和授权
- **bcrypt**: 密码加密
- **MySQL**: 数据存储

### 前端技术
- **Vue 3**: 渐进式JavaScript框架
- **Composition API**: Vue3新特性
- **原生CSS**: 响应式样式设计
- **Fetch API**: HTTP请求处理

## 📊 功能特性

### 🔐 用户认证
- JWT令牌认证
- 密码加密存储
- 自动登录状态保持
- 安全登出机制

### 📈 数据展示
- TOP20股票实时排行榜
- 涨跌幅颜色标识
- 统计信息卡片展示
- 响应式表格设计

### 🔄 自动更新
- 每分钟自动刷新数据
- 手动刷新功能
- 自动刷新开关控制
- 实时状态指示

### 🎨 用户界面
- 现代化登录界面
- 渐变色彩设计
- 响应式布局
- 友好的错误提示

## 🔌 API接口

### 认证接口
- `POST /api/login` - 用户登录
- `GET /api/user/me` - 获取用户信息

### 数据接口
- `GET /api/top20` - 获取TOP20股票数据
- `GET /api/health` - 健康检查

### 响应格式
```json
{
  "stocks": [
    {
      "rank": 1,
      "stock_code": "000001",
      "stock_name": "平安银行",
      "current_price": 12.50,
      "change_pct": 2.45,
      "entry_date": "2025-08-01",
      "concepts": "银行、金融科技"
    }
  ],
  "update_time": "2025-08-11 16:45:00",
  "total_count": 20,
  "positive_count": 15,
  "avg_change": 1.23,
  "max_gain": 5.67
}
```

## 🗄️ 数据库设计

### top20_cache 表
```sql
CREATE TABLE top20_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rank_num INT NOT NULL COMMENT '排名',
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    change_pct DECIMAL(8,2) NOT NULL COMMENT '涨跌幅(%)',
    entry_date DATE NOT NULL COMMENT '入选日期',
    concepts TEXT COMMENT '概念',
    entry_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '入选价格',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 🔄 数据流程

1. **数据采集**: `task_scheduler.py` 定时执行 `send_top20_to_dingtalk.py`
2. **数据存储**: 实时股票数据保存到 `top20_cache` 表
3. **API服务**: `backend_api.py` 提供RESTful接口
4. **前端展示**: Vue3应用从API获取数据并渲染

## 🛠️ 开发指南

### 添加新功能
1. 后端：在 `backend_api.py` 中添加新的API端点
2. 前端：在 `app_fixed.js` 中添加对应的处理逻辑
3. 数据库：如需要，在 `init_web_database.py` 中添加新表

### 调试技巧
```bash
# 查看后端日志
tail -f backend.log

# 查看前端日志
tail -f frontend.log

# 检查API状态
curl http://127.0.0.1:8000/api/health

# 测试登录
curl -X POST http://127.0.0.1:8000/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}'
```

## 🔧 故障排除

### 常见问题

1. **页面白屏**
   - 检查浏览器控制台错误
   - 确认JavaScript文件加载正常
   - 验证API服务是否启动

2. **登录失败**
   - 检查用户名密码是否正确
   - 确认后端API服务正常运行
   - 查看网络请求是否成功

3. **数据不显示**
   - 检查数据库连接
   - 确认 `top20_cache` 表有数据
   - 验证API返回数据格式

4. **CORS错误**
   - 确认后端CORS配置正确
   - 检查前后端端口是否正确

### 日志分析
```bash
# 后端错误日志
grep "ERROR" backend.log

# 前端访问日志
grep "GET\|POST" frontend.log

# 数据库连接测试
python3 -c "
from sqlalchemy import create_engine
from config import Config
engine = create_engine(Config.DATABASE_URL)
with engine.connect() as conn:
    result = conn.execute('SELECT COUNT(*) FROM top20_cache')
    print(f'缓存数据条数: {result.fetchone()[0]}')
"
```

## 🚀 部署建议

### 生产环境
1. 使用Nginx反向代理
2. 配置HTTPS证书
3. 设置环境变量管理配置
4. 使用进程管理器(如PM2)
5. 配置日志轮转

### 性能优化
1. 启用Gzip压缩
2. 配置静态资源缓存
3. 数据库连接池优化
4. API响应缓存

## 📞 技术支持

如遇问题，请检查：
1. 虚拟环境是否正确激活
2. 所有依赖是否安装完整
3. 数据库服务是否正常运行
4. 端口是否被其他程序占用

---

**注意**: 这是一个演示系统，生产环境使用前请进行充分的安全性和性能测试。
