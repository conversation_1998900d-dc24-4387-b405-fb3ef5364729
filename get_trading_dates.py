#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取A股交易数据并存储到数据库的脚本
"""
import requests
import json
from sqlalchemy import create_engine, Column, String, Integer
from sqlalchemy.orm import declarative_base
from sqlalchemy import inspect
from sqlalchemy.orm import sessionmaker
import datetime

# 数据库配置
# DB_CONFIG = 'mysql+pymysql://root:root@*************:3306/data_base'
DB_CONFIG = 'mysql+pymysql://root:tanxi219.@localhost:3306/data_base'


# 创建数据库引擎和会话
engine = create_engine(DB_CONFIG)
Session = sessionmaker(bind=engine)

base = declarative_base()

# 定义数据模型
class StockMarketData(base):
    __tablename__ = 'stock_market_data'

    trade_day = Column(String(10), primary_key=True, comment='交易日期')
    strong = Column(Integer, comment='情绪强度')
    ztjs = Column(Integer, comment='涨停家数')
    lbgd = Column(Integer, comment='连板高度')
    df_num = Column(Integer, comment='大幅回撤个数')

def drop_table_if_exists():
    """如果表存在则删除表"""
    inspector = inspect(engine)
    if inspector.has_table('stock_market_data'):
        base.metadata.tables['stock_market_data'].drop(engine)
        print("已删除旧表 stock_market_data")

def create_table():
    """创建数据库表"""
    base.metadata.create_all(engine)
    print("已创建新表 stock_market_data，包含字段备注")


def get_market_data(days=10):
    """从API获取市场数据"""
    url = f'https://apphis.longhuvip.com/w1/api/index.php?Index=0&PhoneOSNew=2&VerSion=********&a=ChangeStatistics&apiv=w40&c=HisHomeDingPan&st={days}'
    try:
        response = requests.get(url)
        response.raise_for_status()  # 检查请求是否成功
        data = response.json()

        if data.get('errcode') == '0' and 'info' in data:
            return data['info']
        else:
            print(f"API返回错误: {data.get('errcode')}, {data.get('errmsg')}")
            return []
    except Exception as e:
        print(f"获取数据失败: {str(e)}")
        return []


def save_to_database(market_data):
    """保存数据到数据库"""
    session = Session()
    try:
        for item in market_data:
            # 重命名Day为trade_day
            trade_data = StockMarketData(
                trade_day=item['Day'],
                strong=int(item['strong']),
                ztjs=int(item['ztjs']),
                lbgd=int(item['lbgd']),
                df_num=int(item['df_num'])
            )
            # 检查是否已存在，如果存在则更新
            existing = session.query(StockMarketData).filter_by(trade_day=item['Day']).first()
            if existing:
                existing.strong = int(item['strong'])
                existing.ztjs = int(item['ztjs'])
                existing.lbgd = int(item['lbgd'])
                existing.df_num = int(item['df_num'])
            else:
                session.add(trade_data)
        session.commit()
        print(f"成功保存 {len(market_data)} 条数据到数据库")
    except Exception as e:
        session.rollback()
        print(f"保存数据失败: {str(e)}")
    finally:
        session.close()


def get_recent_trading_dates(months=5):
    """获取最近指定月数的A股交易日日期"""
    # 计算日期范围
    end_date = datetime.datetime.now()
    start_date = end_date - datetime.timedelta(days=months*30)

    # 获取市场数据（足够大的天数以确保覆盖所需月份）
    days_needed = (end_date - start_date).days + 30  # 额外加30天以确保覆盖所有交易日
    market_data = get_market_data(days=days_needed)

    # 提取交易日期并过滤在范围内的日期
    trading_dates = []
    for item in market_data:
        trade_date = datetime.datetime.strptime(item['Day'], '%Y-%m-%d')
        if start_date <= trade_date <= end_date:
            trading_dates.append(item['Day'])

    # 排序
    trading_dates.sort()
    return trading_dates


if __name__ == '__main__':
    # 删除旧表（如果存在）并创建新表
    drop_table_if_exists()
    create_table()

    # 获取并保存市场数据
    days = 150  # 获取最近150天的数据，大约5个月
    market_data = get_market_data(days=days)
    if market_data:
        save_to_database(market_data)

    # 获取最近5个月的交易日日期并打印
    try:
        trading_dates = get_recent_trading_dates(months=5)
        print(f"最近5个月的A股交易日日期({len(trading_dates)}天):")
        for date in trading_dates:
            print(date)
    except Exception as e:
        print(f"获取交易日历失败: {str(e)}")