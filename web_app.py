#!/usr/bin/env python3
"""
股票TOP20监控Web应用
基于FastAPI + Naive UI的前后端分离应用
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException, Depends, status, Request
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# JWT配置
SECRET_KEY = "your-secret-key-here-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer()

# 创建FastAPI应用
app = FastAPI(
    title="股票TOP20监控系统",
    description="实时监控策略股票TOP20排行榜",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据库连接
engine = create_engine(Config.DATABASE_URL)

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class StockData(BaseModel):
    rank: int
    stock_code: str
    stock_name: str
    current_price: float
    change_pct: float
    entry_date: str
    concepts: str
    entry_price: Optional[float] = None

class Top20Response(BaseModel):
    stocks: List[StockData]
    update_time: str
    total_count: int
    positive_count: int
    avg_change: float
    max_gain: float

# 用户验证（简单的硬编码用户，生产环境应该使用数据库）
USERS_DB = {
    "admin": {
        "username": "admin",
        "hashed_password": pwd_context.hash("admin123"),  # 密码: admin123
        "role": "admin"
    },
    "user": {
        "username": "user", 
        "hashed_password": pwd_context.hash("user123"),   # 密码: user123
        "role": "user"
    }
}

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """验证用户"""
    user = USERS_DB.get(username)
    if not user:
        return None
    if not verify_password(password, user["hashed_password"]):
        return None
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = USERS_DB.get(username)
    if user is None:
        raise credentials_exception
    return user

# API路由
@app.post("/api/login", response_model=Token)
async def login(login_data: LoginRequest):
    """用户登录"""
    user = authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/api/user/me")
async def read_users_me(current_user: dict = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "username": current_user["username"],
        "role": current_user["role"]
    }

def get_latest_strategy_stocks() -> List[Dict]:
    """从数据库获取最新的策略股票数据"""
    try:
        with engine.connect() as conn:
            # 获取最近5个交易日的策略股票
            result = conn.execute(text("""
                SELECT DISTINCT stock_code, stock_name, concepts, entry_date, entry_price
                FROM new_strategy_results 
                WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 10 DAY)
                ORDER BY created_at DESC
            """))
            
            rows = result.fetchall()
            stocks = []
            for row in rows:
                stocks.append({
                    'stock_code': row[0],
                    'stock_name': row[1], 
                    'concepts': row[2] or '',
                    'entry_date': row[3],
                    'entry_price': float(row[4]) if row[4] else 0.0
                })
            
            logger.info(f"从数据库获取到 {len(stocks)} 个策略股票")
            return stocks
            
    except Exception as e:
        logger.error(f"获取策略股票数据失败: {str(e)}")
        return []

def get_realtime_stock_data(stock_codes: List[str]) -> Dict[str, Dict]:
    """获取股票实时行情数据"""
    import requests
    import time
    import random

    realtime_data = {}

    try:
        # 使用东方财富API获取实时行情
        # 将股票代码转换为东方财富格式
        ef_codes = []
        for code in stock_codes:
            if code.startswith('6'):
                ef_codes.append(f"1.{code}")  # 上海
            else:
                ef_codes.append(f"0.{code}")  # 深圳

        # 分批请求，每次最多50个股票
        batch_size = 50
        for i in range(0, len(ef_codes), batch_size):
            batch_codes = ef_codes[i:i + batch_size]
            codes_str = ','.join(batch_codes)

            url = f"http://push2.eastmoney.com/api/qt/ulist.np/get"
            params = {
                'fltt': '2',
                'invt': '2',
                'fields': 'f2,f3,f12,f14',  # f2=最新价, f3=涨跌幅, f12=代码, f14=名称
                'secids': codes_str
            }

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            try:
                response = requests.get(url, params=params, headers=headers, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('data') and data['data'].get('diff'):
                        for item in data['data']['diff']:
                            code = item.get('f12')  # 股票代码
                            price = item.get('f2')  # 最新价
                            change_pct = item.get('f3')  # 涨跌幅

                            if code and price is not None and change_pct is not None:
                                realtime_data[code] = {
                                    'current_price': round(float(price), 2),
                                    'change_pct': round(float(change_pct), 2)
                                }

                # 添加延迟避免请求过快
                time.sleep(0.5)

            except Exception as e:
                logger.warning(f"获取批次数据失败: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"获取实时行情失败: {str(e)}")

    # 对于没有获取到数据的股票，使用模拟数据
    for code in stock_codes:
        if code not in realtime_data:
            logger.warning(f"股票 {code} 未获取到实时数据，使用模拟数据")
            base_price = random.uniform(10, 100)
            change_pct = random.uniform(-5, 10)
            current_price = base_price * (1 + change_pct / 100)

            realtime_data[code] = {
                'current_price': round(current_price, 2),
                'change_pct': round(change_pct, 2)
            }

    logger.info(f"获取到 {len(realtime_data)} 个股票的实时行情数据")
    return realtime_data

def get_top20_from_cache() -> List[Dict]:
    """从缓存表获取TOP20数据"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT rank_num, stock_code, stock_name, current_price, change_pct,
                       entry_date, concepts, entry_price, update_time
                FROM top20_cache
                ORDER BY rank_num
                LIMIT 20
            """))

            rows = result.fetchall()
            stocks = []
            for row in rows:
                stocks.append({
                    'rank': row[0],
                    'stock_code': row[1],
                    'stock_name': row[2],
                    'current_price': float(row[3]),
                    'change_pct': float(row[4]),
                    'entry_date': str(row[5]),
                    'concepts': row[6] or '',
                    'entry_price': float(row[7]) if row[7] else 0.0,
                    'update_time': str(row[8])
                })

            logger.info(f"从缓存表获取到 {len(stocks)} 个TOP20股票")
            return stocks

    except Exception as e:
        logger.error(f"从缓存表获取TOP20数据失败: {str(e)}")
        return []

@app.get("/api/top20", response_model=Top20Response)
async def get_top20_stocks(current_user: dict = Depends(get_current_user)):
    """获取TOP20股票数据"""
    try:
        # 首先尝试从缓存表获取数据
        cached_stocks = get_top20_from_cache()

        if cached_stocks:
            # 使用缓存数据
            stocks_with_rank = []
            for stock in cached_stocks:
                stocks_with_rank.append(StockData(
                    rank=stock['rank'],
                    stock_code=stock['stock_code'],
                    stock_name=stock['stock_name'],
                    current_price=stock['current_price'],
                    change_pct=stock['change_pct'],
                    entry_date=stock['entry_date'],
                    concepts=stock['concepts'],
                    entry_price=stock['entry_price']
                ))

            # 计算统计数据
            positive_count = len([s for s in cached_stocks if s['change_pct'] > 0])
            avg_change = sum(s['change_pct'] for s in cached_stocks) / len(cached_stocks) if cached_stocks else 0
            max_gain = max(s['change_pct'] for s in cached_stocks) if cached_stocks else 0
            update_time = cached_stocks[0]['update_time'] if cached_stocks else datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            return Top20Response(
                stocks=stocks_with_rank,
                update_time=update_time,
                total_count=len(cached_stocks),
                positive_count=positive_count,
                avg_change=round(avg_change, 2),
                max_gain=round(max_gain, 2)
            )

        # 如果缓存为空，则实时计算（备用方案）
        logger.warning("缓存表为空，使用实时计算")
        strategy_stocks = get_latest_strategy_stocks()

        if not strategy_stocks:
            return Top20Response(
                stocks=[],
                update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                total_count=0,
                positive_count=0,
                avg_change=0.0,
                max_gain=0.0
            )

        # 获取实时行情数据
        stock_codes = [stock['stock_code'] for stock in strategy_stocks]
        realtime_data = get_realtime_stock_data(stock_codes)

        # 合并数据并计算排名
        combined_stocks = []
        for stock in strategy_stocks:
            code = stock['stock_code']
            if code in realtime_data:
                rt_data = realtime_data[code]
                combined_stocks.append({
                    'stock_code': code,
                    'stock_name': stock['stock_name'],
                    'current_price': rt_data['current_price'],
                    'change_pct': rt_data['change_pct'],
                    'entry_date': stock['entry_date'],
                    'concepts': stock['concepts'][:20] + "..." if len(stock['concepts']) > 20 else stock['concepts'],
                    'entry_price': stock['entry_price']
                })

        # 按涨跌幅排序，取前20
        combined_stocks.sort(key=lambda x: x['change_pct'], reverse=True)
        top20_stocks = combined_stocks[:20]

        # 计算统计数据
        positive_count = len([s for s in top20_stocks if s['change_pct'] > 0])
        avg_change = sum(s['change_pct'] for s in top20_stocks) / len(top20_stocks) if top20_stocks else 0
        max_gain = max(s['change_pct'] for s in top20_stocks) if top20_stocks else 0

        # 添加排名
        stocks_with_rank = []
        for i, stock in enumerate(top20_stocks, 1):
            stocks_with_rank.append(StockData(
                rank=i,
                **stock
            ))

        return Top20Response(
            stocks=stocks_with_rank,
            update_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            total_count=len(top20_stocks),
            positive_count=positive_count,
            avg_change=round(avg_change, 2),
            max_gain=round(max_gain, 2)
        )

    except Exception as e:
        logger.error(f"获取TOP20数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 静态文件服务
@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回前端页面"""
    return """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>股票TOP20监控系统</title>
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <script src="https://unpkg.com/naive-ui@2.34.4/dist/index.js"></script>
        <link rel="stylesheet" href="https://unpkg.com/naive-ui@2.34.4/dist/index.css">
        <style>
            body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .stats-card { margin-bottom: 20px; }
            .positive { color: #18a058; }
            .negative { color: #d03050; }
            .neutral { color: #909399; }
        </style>
    </head>
    <body>
        <div id="app"></div>
        <script>
            const { createApp, ref, reactive, onMounted, computed } = Vue;
            const {
                NConfigProvider, NMessageProvider, NDialogProvider, NNotificationProvider,
                NLayout, NLayoutHeader, NLayoutContent, NLayoutSider,
                NMenu, NCard, NButton, NForm, NFormItem, NInput, NSpace,
                NDataTable, NTag, NStatistic, NGrid, NGridItem, NSpin,
                NAlert, NIcon, NTime, darkTheme, useMessage, useDialog, useNotification
            } = naive;

            // API工具函数
            const api = {
                baseURL: '',
                token: localStorage.getItem('token'),

                setToken(token) {
                    this.token = token;
                    localStorage.setItem('token', token);
                },

                clearToken() {
                    this.token = null;
                    localStorage.removeItem('token');
                },

                async request(url, options = {}) {
                    const config = {
                        headers: {
                            'Content-Type': 'application/json',
                            ...(this.token && { 'Authorization': `Bearer ${this.token}` })
                        },
                        ...options
                    };

                    const response = await fetch(this.baseURL + url, config);

                    if (response.status === 401) {
                        this.clearToken();
                        throw new Error('登录已过期，请重新登录');
                    }

                    if (!response.ok) {
                        const error = await response.json().catch(() => ({ detail: '请求失败' }));
                        throw new Error(error.detail || '请求失败');
                    }

                    return response.json();
                },

                async login(username, password) {
                    const response = await this.request('/api/login', {
                        method: 'POST',
                        body: JSON.stringify({ username, password })
                    });
                    this.setToken(response.access_token);
                    return response;
                },

                async getTop20() {
                    return this.request('/api/top20');
                },

                async getUserInfo() {
                    return this.request('/api/user/me');
                }
            };

            // 主应用组件
            const App = {
                setup() {
                    const message = useMessage();
                    const dialog = useDialog();
                    const notification = useNotification();

                    const state = reactive({
                        isLoggedIn: !!api.token,
                        user: null,
                        loading: false,
                        top20Data: null,
                        autoRefresh: true,
                        refreshInterval: null
                    });

                    const loginForm = reactive({
                        username: '',
                        password: '',
                        loading: false
                    });

                    // 登录
                    const handleLogin = async () => {
                        if (!loginForm.username || !loginForm.password) {
                            message.error('请输入用户名和密码');
                            return;
                        }

                        loginForm.loading = true;
                        try {
                            await api.login(loginForm.username, loginForm.password);
                            state.isLoggedIn = true;
                            message.success('登录成功');
                            await loadUserInfo();
                            await loadTop20Data();
                            startAutoRefresh();
                        } catch (error) {
                            message.error(error.message);
                        } finally {
                            loginForm.loading = false;
                        }
                    };

                    // 登出
                    const handleLogout = () => {
                        dialog.warning({
                            title: '确认登出',
                            content: '确定要退出登录吗？',
                            positiveText: '确定',
                            negativeText: '取消',
                            onPositiveClick: () => {
                                api.clearToken();
                                state.isLoggedIn = false;
                                state.user = null;
                                state.top20Data = null;
                                stopAutoRefresh();
                                message.success('已退出登录');
                            }
                        });
                    };

                    // 加载用户信息
                    const loadUserInfo = async () => {
                        try {
                            state.user = await api.getUserInfo();
                        } catch (error) {
                            console.error('加载用户信息失败:', error);
                        }
                    };

                    // 加载TOP20数据
                    const loadTop20Data = async () => {
                        state.loading = true;
                        try {
                            state.top20Data = await api.getTop20();
                        } catch (error) {
                            message.error('加载数据失败: ' + error.message);
                        } finally {
                            state.loading = false;
                        }
                    };

                    // 自动刷新
                    const startAutoRefresh = () => {
                        if (state.refreshInterval) return;
                        state.refreshInterval = setInterval(() => {
                            if (state.autoRefresh && state.isLoggedIn) {
                                loadTop20Data();
                            }
                        }, 60000); // 每分钟刷新一次
                    };

                    const stopAutoRefresh = () => {
                        if (state.refreshInterval) {
                            clearInterval(state.refreshInterval);
                            state.refreshInterval = null;
                        }
                    };

                    // 表格列定义
                    const columns = [
                        {
                            title: '排名',
                            key: 'rank',
                            width: 80,
                            render: (row) => h('strong', row.rank)
                        },
                        {
                            title: '股票代码',
                            key: 'stock_code',
                            width: 100
                        },
                        {
                            title: '股票名称',
                            key: 'stock_name',
                            width: 120
                        },
                        {
                            title: '当前价格',
                            key: 'current_price',
                            width: 100,
                            render: (row) => `¥${row.current_price}`
                        },
                        {
                            title: '涨跌幅',
                            key: 'change_pct',
                            width: 100,
                            render: (row) => {
                                const pct = row.change_pct;
                                const className = pct > 0 ? 'positive' : pct < 0 ? 'negative' : 'neutral';
                                return h('span', { class: className }, `${pct > 0 ? '+' : ''}${pct}%`);
                            }
                        },
                        {
                            title: '入选日期',
                            key: 'entry_date',
                            width: 120
                        },
                        {
                            title: '概念',
                            key: 'concepts',
                            ellipsis: {
                                tooltip: true
                            }
                        }
                    ];

                    // 组件挂载时的初始化
                    onMounted(async () => {
                        if (state.isLoggedIn) {
                            await loadUserInfo();
                            await loadTop20Data();
                            startAutoRefresh();
                        }
                    });

                    return {
                        state,
                        loginForm,
                        columns,
                        handleLogin,
                        handleLogout,
                        loadTop20Data,
                        startAutoRefresh,
                        stopAutoRefresh
                    };
                },

                template: `
                    <n-config-provider>
                        <n-message-provider>
                            <n-dialog-provider>
                                <n-notification-provider>
                                    <div class="container">
                                        <!-- 登录界面 -->
                                        <div v-if="!state.isLoggedIn">
                                            <div class="header">
                                                <h1>股票TOP20监控系统</h1>
                                                <p>请登录以查看实时数据</p>
                                            </div>
                                            <n-card style="max-width: 400px; margin: 0 auto;">
                                                <n-form>
                                                    <n-form-item label="用户名">
                                                        <n-input
                                                            v-model:value="loginForm.username"
                                                            placeholder="请输入用户名"
                                                            @keyup.enter="handleLogin"
                                                        />
                                                    </n-form-item>
                                                    <n-form-item label="密码">
                                                        <n-input
                                                            v-model:value="loginForm.password"
                                                            type="password"
                                                            placeholder="请输入密码"
                                                            @keyup.enter="handleLogin"
                                                        />
                                                    </n-form-item>
                                                    <n-form-item>
                                                        <n-button
                                                            type="primary"
                                                            block
                                                            :loading="loginForm.loading"
                                                            @click="handleLogin"
                                                        >
                                                            登录
                                                        </n-button>
                                                    </n-form-item>
                                                </n-form>
                                                <n-alert type="info" style="margin-top: 16px;">
                                                    <div>测试账号：</div>
                                                    <div>管理员 - 用户名: admin, 密码: admin123</div>
                                                    <div>普通用户 - 用户名: user, 密码: user123</div>
                                                </n-alert>
                                            </n-card>
                                        </div>

                                        <!-- 主界面 -->
                                        <div v-else>
                                            <!-- 头部 -->
                                            <n-layout-header style="padding: 16px; border-bottom: 1px solid #e0e0e6;">
                                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                                    <h2 style="margin: 0;">股票TOP20监控系统</h2>
                                                    <n-space>
                                                        <span>欢迎, {{ state.user?.username }}</span>
                                                        <n-button @click="loadTop20Data" :loading="state.loading">刷新数据</n-button>
                                                        <n-button @click="handleLogout">退出登录</n-button>
                                                    </n-space>
                                                </div>
                                            </n-layout-header>

                                            <!-- 内容区域 -->
                                            <n-layout-content style="padding: 24px;">
                                                <n-spin :show="state.loading">
                                                    <div v-if="state.top20Data">
                                                        <!-- 统计信息 -->
                                                        <n-grid :cols="4" :x-gap="16" class="stats-card">
                                                            <n-grid-item>
                                                                <n-card>
                                                                    <n-statistic label="总数量" :value="state.top20Data.total_count" />
                                                                </n-card>
                                                            </n-grid-item>
                                                            <n-grid-item>
                                                                <n-card>
                                                                    <n-statistic label="上涨数量" :value="state.top20Data.positive_count" />
                                                                </n-card>
                                                            </n-grid-item>
                                                            <n-grid-item>
                                                                <n-card>
                                                                    <n-statistic
                                                                        label="平均涨幅"
                                                                        :value="state.top20Data.avg_change"
                                                                        suffix="%"
                                                                    />
                                                                </n-card>
                                                            </n-grid-item>
                                                            <n-grid-item>
                                                                <n-card>
                                                                    <n-statistic
                                                                        label="最高涨幅"
                                                                        :value="state.top20Data.max_gain"
                                                                        suffix="%"
                                                                    />
                                                                </n-card>
                                                            </n-grid-item>
                                                        </n-grid>

                                                        <!-- 数据表格 -->
                                                        <n-card title="TOP20股票排行榜">
                                                            <template #header-extra>
                                                                <span style="font-size: 14px; color: #909399;">
                                                                    更新时间: {{ state.top20Data.update_time }}
                                                                </span>
                                                            </template>
                                                            <n-data-table
                                                                :columns="columns"
                                                                :data="state.top20Data.stocks"
                                                                :pagination="false"
                                                                :bordered="false"
                                                                size="small"
                                                            />
                                                        </n-card>
                                                    </div>
                                                    <div v-else style="text-align: center; padding: 40px;">
                                                        <p>暂无数据</p>
                                                    </div>
                                                </n-spin>
                                            </n-layout-content>
                                        </div>
                                    </div>
                                </n-notification-provider>
                            </n-dialog-provider>
                        </n-message-provider>
                    </n-config-provider>
                `
            };

            // 创建并挂载应用
            createApp(App).mount('#app');
        </script>
    </body>
    </html>
    """

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("web_app:app", host="0.0.0.0", port=8000, reload=True)
