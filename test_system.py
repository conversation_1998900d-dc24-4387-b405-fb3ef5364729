#!/usr/bin/env python3
"""
系统测试脚本
测试整个股票TOP20监控系统的工作流程
"""

import requests
import time
import subprocess
import sys
from datetime import datetime

def test_backend_api():
    """测试后端API"""
    print("🔧 测试后端API...")
    
    try:
        # 测试健康检查
        response = requests.get("http://127.0.0.1:8000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端API健康检查通过")
            return True
        else:
            print(f"❌ 后端API健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端API连接失败: {str(e)}")
        return False

def test_frontend():
    """测试前端服务"""
    print("🌐 测试前端服务...")
    
    try:
        response = requests.get("http://127.0.0.1:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {str(e)}")
        return False

def test_login():
    """测试登录功能"""
    print("🔐 测试登录功能...")
    
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        response = requests.post(
            "http://127.0.0.1:8000/api/login",
            json=login_data,
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if "access_token" in data:
                print("✅ 登录功能正常")
                return data["access_token"]
            else:
                print("❌ 登录响应格式错误")
                return None
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录测试失败: {str(e)}")
        return None

def test_top20_api(token):
    """测试TOP20数据API"""
    print("📊 测试TOP20数据API...")
    
    try:
        headers = {
            "Authorization": f"Bearer {token}"
        }
        
        response = requests.get(
            "http://127.0.0.1:8000/api/top20",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if "stocks" in data:
                stock_count = len(data["stocks"])
                print(f"✅ TOP20数据API正常，获取到 {stock_count} 条股票数据")
                
                if stock_count > 0:
                    print(f"📈 示例数据: {data['stocks'][0]['stock_name']} ({data['stocks'][0]['change_pct']}%)")
                
                return True
            else:
                print("❌ TOP20数据格式错误")
                return False
        else:
            print(f"❌ TOP20数据API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ TOP20数据测试失败: {str(e)}")
        return False

def test_data_update():
    """测试数据更新功能"""
    print("🔄 测试数据更新功能...")
    
    try:
        # 运行数据更新脚本
        result = subprocess.run(
            [sys.executable, "send_top20_to_dingtalk.py", "5"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ 数据更新脚本执行成功")
            if "TOP20数据已保存到数据库缓存" in result.stdout:
                print("✅ 数据库缓存更新成功")
                return True
            else:
                print("⚠️ 数据更新成功但未确认缓存更新")
                return True
        else:
            print(f"❌ 数据更新脚本执行失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 数据更新测试失败: {str(e)}")
        return False

def test_database():
    """测试数据库连接和数据"""
    print("🗄️ 测试数据库...")
    
    try:
        from sqlalchemy import create_engine, text
        from config import Config
        
        engine = create_engine(Config.DATABASE_URL)
        with engine.connect() as conn:
            # 检查表是否存在
            result = conn.execute(text("SHOW TABLES LIKE 'top20_cache'"))
            if not result.fetchone():
                print("❌ top20_cache表不存在")
                return False
            
            # 检查数据
            result = conn.execute(text("SELECT COUNT(*) FROM top20_cache"))
            count = result.fetchone()[0]
            
            if count > 0:
                print(f"✅ 数据库连接正常，缓存表有 {count} 条数据")
                return True
            else:
                print("⚠️ 数据库连接正常，但缓存表为空")
                return True
                
    except Exception as e:
        print(f"❌ 数据库测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 股票TOP20监控系统测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试数据库
    test_results.append(("数据库", test_database()))
    
    # 2. 测试后端API
    test_results.append(("后端API", test_backend_api()))
    
    # 3. 测试前端服务
    test_results.append(("前端服务", test_frontend()))
    
    # 4. 测试登录
    token = test_login()
    test_results.append(("登录功能", token is not None))
    
    # 5. 测试TOP20 API
    if token:
        test_results.append(("TOP20数据API", test_top20_api(token)))
    else:
        test_results.append(("TOP20数据API", False))
    
    # 6. 测试数据更新
    test_results.append(("数据更新", test_data_update()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        print("\n💡 使用提示:")
        print("   - 前端地址: http://127.0.0.1:3000")
        print("   - 后端API: http://127.0.0.1:8000/docs")
        print("   - 测试账号: admin/admin123")
    else:
        print("⚠️ 部分测试失败，请检查相关服务")
        
        if not test_results[1][1]:  # 后端API失败
            print("   请启动后端服务: python3 -m uvicorn backend_api:app --host 127.0.0.1 --port 8000")
        
        if not test_results[2][1]:  # 前端服务失败
            print("   请启动前端服务: python3 -m uvicorn frontend_server:app --host 127.0.0.1 --port 3000")

if __name__ == "__main__":
    main()
