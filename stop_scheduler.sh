#!/bin/bash

# 股票数据处理任务调度器停止脚本

echo "🛑 停止股票数据处理任务调度器"
echo "=================================="

# 检查PID文件
if [ -f "logs/scheduler.pid" ]; then
    PID=$(cat logs/scheduler.pid)
    
    # 检查进程是否存在
    if ps -p $PID > /dev/null 2>&1; then
        echo "📍 找到调度器进程 (PID: $PID)"
        
        # 发送停止信号
        echo "🔄 正在停止调度器..."
        kill $PID
        
        # 等待进程结束
        sleep 3
        
        # 检查是否已停止
        if ps -p $PID > /dev/null 2>&1; then
            echo "⚠️ 进程未响应，强制终止..."
            kill -9 $PID
            sleep 1
        fi
        
        # 再次检查
        if ps -p $PID > /dev/null 2>&1; then
            echo "❌ 无法停止进程 $PID"
            exit 1
        else
            echo "✅ 调度器已停止"
            rm -f logs/scheduler.pid
        fi
    else
        echo "⚠️ PID文件存在但进程不存在，清理PID文件"
        rm -f logs/scheduler.pid
    fi
else
    echo "📍 未找到PID文件，尝试查找进程..."
    
    # 查找task_scheduler.py进程
    PIDS=$(pgrep -f "python.*task_scheduler.py")
    
    if [ -n "$PIDS" ]; then
        echo "📍 找到调度器进程: $PIDS"
        
        for PID in $PIDS; do
            echo "🔄 停止进程 $PID..."
            kill $PID
        done
        
        sleep 3
        
        # 检查是否还有进程
        REMAINING=$(pgrep -f "python.*task_scheduler.py")
        if [ -n "$REMAINING" ]; then
            echo "⚠️ 强制终止剩余进程: $REMAINING"
            pkill -9 -f "python.*task_scheduler.py"
        fi
        
        echo "✅ 所有调度器进程已停止"
    else
        echo "📍 未找到运行中的调度器进程"
    fi
fi

# 发送停止通知
echo "📱 发送停止通知..."
python3 -c "
from task_scheduler import DingTalkNotifier
webhook_url = 'https://oapi.dingtalk.com/robot/send?access_token=a4ca5597fb378abffcf3c8f894cdf5909bc1382edc49304cd42a4e8ed9151e55'
notifier = DingTalkNotifier(webhook_url)
notifier.send_message('调度器已停止', '股票数据处理任务调度器已手动停止')
print('✅ 停止通知已发送')
" 2>/dev/null || echo "⚠️ 停止通知发送失败"

echo "=================================="
echo "🎯 调度器停止完成"
