# 股票TOP20监控系统 - 项目总结

## 🎯 项目完成情况

✅ **已完成所有需求**：
1. ✅ 前后端分离架构
2. ✅ 登录认证系统
3. ✅ TOP20股票数据展示
4. ✅ 实时数据更新和缓存
5. ✅ 与任务调度器集成
6. ✅ 钉钉消息推送
7. ✅ 数据库自动保存

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    股票TOP20监控系统                          │
├─────────────────────────────────────────────────────────────┤
│  前端 (Vue3)     │  后端 (FastAPI)  │  数据库 (MySQL)      │
│  Port: 3000      │  Port: 8000      │  top20_cache        │
├─────────────────────────────────────────────────────────────┤
│              任务调度器 (task_scheduler.py)                  │
│              数据获取 (send_top20_to_dingtalk.py)           │
└─────────────────────────────────────────────────────────────┘
```

## 📁 核心文件说明

### 🔧 后端文件
- **`backend_api.py`** - FastAPI后端服务，提供RESTful API
- **`frontend_server.py`** - 前端静态文件服务器
- **`send_top20_to_dingtalk.py`** - 数据获取和钉钉推送（已集成数据库保存）
- **`task_scheduler.py`** - 任务调度器（交易时间内每分钟执行）
- **`init_web_database.py`** - 数据库初始化脚本

### 🌐 前端文件
- **`frontend/public/index.html`** - 主页面
- **`frontend/src/app_fixed.js`** - Vue3应用逻辑
- **`frontend/src/simple_app.js`** - 简化测试应用

### 🚀 启动脚本
- **`start_frontend_backend.sh`** - 一键启动前后端服务
- **`stop_frontend_backend.sh`** - 停止所有服务
- **`test_system.py`** - 系统完整性测试

## 🔄 数据流程

1. **数据采集**: `task_scheduler.py` 在交易时间内每分钟调用 `send_top20_to_dingtalk.py`
2. **实时行情**: 从腾讯财经API获取股票实时价格和涨跌幅
3. **数据处理**: 按涨跌幅排序，生成TOP20排行榜
4. **数据保存**: 自动保存到 `top20_cache` 数据库表
5. **消息推送**: 发送格式化消息到钉钉群
6. **前端展示**: Vue3应用从API获取数据并实时展示

## 🎨 功能特性

### 🔐 用户认证
- JWT令牌认证机制
- 测试账号：admin/admin123, user/user123
- 自动登录状态保持
- 安全登出功能

### 📊 数据展示
- TOP20股票实时排行榜
- 涨跌幅颜色标识（红涨绿跌）
- 统计信息卡片（总数、上涨数、平均涨幅、最高涨幅）
- 响应式表格设计
- 排名徽章设计（金银铜牌）

### 🔄 自动更新
- 交易时间内每分钟自动更新
- 手动刷新按钮
- 自动刷新开关控制
- 实时更新时间显示

### 📱 用户界面
- 现代化渐变色登录界面
- 响应式布局设计
- 友好的错误提示
- 加载状态指示

## 🚀 快速启动

### 方式1：一键启动
```bash
./start_frontend_backend.sh
```

### 方式2：手动启动
```bash
# 后端API
python3 -m uvicorn backend_api:app --host 127.0.0.1 --port 8000 &

# 前端服务
python3 -m uvicorn frontend_server:app --host 127.0.0.1 --port 3000 &
```

### 访问地址
- **前端界面**: http://127.0.0.1:3000
- **后端API文档**: http://127.0.0.1:8000/docs
- **健康检查**: http://127.0.0.1:8000/api/health

## 🧪 系统测试

运行完整系统测试：
```bash
python3 test_system.py
```

测试覆盖：
- ✅ 数据库连接和数据
- ✅ 后端API健康检查
- ✅ 前端服务状态
- ✅ 用户登录功能
- ✅ TOP20数据API
- ✅ 数据更新功能

## 📊 数据库设计

### top20_cache 表结构
```sql
CREATE TABLE top20_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rank_num INT NOT NULL COMMENT '排名',
    stock_code VARCHAR(10) NOT NULL COMMENT '股票代码',
    stock_name VARCHAR(50) NOT NULL COMMENT '股票名称',
    current_price DECIMAL(10,2) NOT NULL COMMENT '当前价格',
    change_pct DECIMAL(8,2) NOT NULL COMMENT '涨跌幅(%)',
    entry_date DATE NOT NULL COMMENT '入选日期',
    concepts TEXT COMMENT '概念',
    entry_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '入选价格',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
);
```

## 🔌 API接口

### 认证接口
- `POST /api/login` - 用户登录
- `GET /api/user/me` - 获取用户信息

### 数据接口
- `GET /api/top20` - 获取TOP20股票数据
- `GET /api/health` - 系统健康检查

## ⚙️ 配置说明

### 交易时间设置
- **上午**: 9:30-11:30
- **下午**: 13:00-15:00
- **频率**: 每分钟执行一次

### 数据源配置
- **股票数据**: 腾讯财经API
- **策略股票**: MySQL数据库 `new_strategy_results` 表
- **缓存存储**: MySQL数据库 `top20_cache` 表

## 🎉 项目亮点

1. **完全前后端分离**: 独立的前端和后端服务，便于扩展和维护
2. **实时数据更新**: 集成任务调度器，自动获取和更新股票数据
3. **数据库自动保存**: 在数据获取的同时自动保存到缓存表
4. **现代化界面**: 基于Vue3的响应式设计，用户体验优秀
5. **完整的测试**: 提供系统完整性测试，确保各组件正常工作
6. **一键部署**: 提供启动和停止脚本，操作简便

## 🔧 技术栈

- **后端**: FastAPI + SQLAlchemy + JWT + bcrypt
- **前端**: Vue 3 + Composition API + 原生CSS
- **数据库**: MySQL
- **任务调度**: Python schedule
- **数据源**: 腾讯财经API
- **消息推送**: 钉钉机器人

## 📞 使用说明

1. **首次使用**: 运行 `python3 init_web_database.py` 初始化数据库
2. **启动系统**: 运行 `./start_frontend_backend.sh`
3. **访问界面**: 打开 http://127.0.0.1:3000
4. **登录系统**: 使用测试账号 admin/admin123
5. **查看数据**: 系统会自动显示TOP20股票排行榜
6. **数据更新**: 系统会在交易时间内每分钟自动更新

## 🎯 总结

本项目成功实现了一个完整的股票TOP20监控系统，具备以下特点：

- ✅ **功能完整**: 涵盖数据获取、处理、存储、展示的完整流程
- ✅ **架构清晰**: 前后端分离，模块化设计
- ✅ **用户友好**: 现代化界面，操作简便
- ✅ **自动化**: 定时任务自动更新数据
- ✅ **可扩展**: 基于标准技术栈，便于后续扩展

系统已通过完整测试，可以投入使用！🚀
