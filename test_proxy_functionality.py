#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sync_kline_by_concepts_chgip import ProxyPool, ConceptBasedKlineSync
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_proxy_pool():
    """测试代理池功能"""
    print("🧪 开始测试代理池功能...")
    
    # 创建代理池
    proxy_pool = ProxyPool()
    
    # 测试获取代理
    print("📋 获取免费代理...")
    proxies = proxy_pool.get_free_proxies()
    print(f"✅ 成功获取 {len(proxies)} 个代理")
    
    # 测试更新代理池
    print("🔄 更新代理池...")
    proxy_pool.update_proxy_pool(force_update=True)
    print(f"✅ 代理池更新完成，当前可用代理数: {len(proxy_pool.proxies)}")
    
    # 测试获取当前代理
    print("🔍 获取当前代理...")
    current_proxy = proxy_pool.get_current_proxy()
    print(f"✅ 当前代理: {current_proxy}")
    
    # 测试切换代理
    print("🔄 测试代理切换...")
    for i in range(min(3, len(proxy_pool.proxies))):
        proxy = proxy_pool.get_current_proxy()
        print(f"  代理 {i+1}: {proxy}")
        proxy_pool.switch_next_proxy()
    
    # 测试标记代理失败
    if proxy_pool.proxies:
        print("❌ 测试标记代理失败...")
        failed_proxy = proxy_pool.proxies[0]
        proxy_pool.mark_proxy_failed(failed_proxy)
        print(f"✅ 已标记代理 {failed_proxy} 为失败")
        print(f"📊 剩余代理数: {len(proxy_pool.proxies)}")
    
    print("🎉 代理池功能测试完成！")
    return True

def test_concept_sync_with_proxy():
    """测试带代理的概念同步器"""
    print("\n🧪 测试带代理的概念同步器...")
    
    try:
        # 创建同步器（使用代理）
        syncer = ConceptBasedKlineSync(use_proxy=True)
        print("✅ 成功创建带代理的同步器")
        
        # 测试代理池初始化
        if syncer.proxy_pool:
            print(f"✅ 代理池已初始化，可用代理数: {len(syncer.proxy_pool.proxies)}")
        else:
            print("⚠️ 代理池未初始化")
        
        # 测试获取市场数据方法（不实际获取数据，只测试方法存在）
        print("🔍 测试获取市场数据方法...")
        if hasattr(syncer, 'get_market_data_with_proxy'):
            print("✅ get_market_data_with_proxy 方法存在")
        else:
            print("❌ get_market_data_with_proxy 方法不存在")
        
        print("🎉 概念同步器测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 概念同步器测试失败: {str(e)}")
        return False

def test_concept_sync_without_proxy():
    """测试不带代理的概念同步器"""
    print("\n🧪 测试不带代理的概念同步器...")
    
    try:
        # 创建同步器（不使用代理）
        syncer = ConceptBasedKlineSync(use_proxy=False)
        print("✅ 成功创建不带代理的同步器")
        
        # 验证代理池为None
        if syncer.proxy_pool is None:
            print("✅ 代理池正确设置为None")
        else:
            print("⚠️ 代理池未正确设置为None")
        
        # 测试获取市场数据方法
        print("🔍 测试获取市场数据方法...")
        if hasattr(syncer, 'get_market_data_with_proxy'):
            print("✅ get_market_data_with_proxy 方法存在")
        else:
            print("❌ get_market_data_with_proxy 方法不存在")
        
        print("🎉 无代理同步器测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 无代理同步器测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始代理功能测试...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        ("代理池功能", test_proxy_pool),
        ("带代理同步器", test_concept_sync_with_proxy),
        ("无代理同步器", test_concept_sync_without_proxy)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试结果总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, result in results if result)
    
    print(f"\n📈 总计: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试均通过！代理功能正常工作。")
    else:
        print("⚠️ 部分测试未通过，请检查相关功能。")
    
    return passed_tests == total_tests

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)