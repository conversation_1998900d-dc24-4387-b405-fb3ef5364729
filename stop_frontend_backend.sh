#!/bin/bash

# 停止前后端服务脚本

echo "🛑 停止股票TOP20监控系统..."

# 从PID文件读取进程ID并停止
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止 (PID: $BACKEND_PID)"
    else
        echo "ℹ️ 后端服务未运行"
    fi
    rm -f backend.pid
fi

if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止 (PID: $FRONTEND_PID)"
    else
        echo "ℹ️ 前端服务未运行"
    fi
    rm -f frontend.pid
fi

# 强制停止相关进程
pkill -f "backend_api:app" 2>/dev/null && echo "🔧 强制停止后端进程"
pkill -f "frontend_server:app" 2>/dev/null && echo "🌐 强制停止前端进程"

echo "✅ 所有服务已停止"
